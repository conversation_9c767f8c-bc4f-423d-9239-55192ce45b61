from sqlmodel import SQLModel, Field, Column
from geoalchemy2 import Geometry
from typing import Optional, Dict, Any, List
from datetime import datetime
from enum import Enum
import uuid

class GeometryType(str, Enum):
    POINT = "point"
    LINESTRING = "linestring"
    POLYGON = "polygon"
    CIRCLE = "circle"
    SECTOR = "sector"

class Sector(SQLModel, table=True):
    """Secteurs géographiques d'opération"""
    __tablename__ = "sectors"
    
    id: Optional[int] = Field(default=None, primary_key=True)
    uuid: str = Field(default_factory=lambda: str(uuid.uuid4()), unique=True, index=True)
    
    # Informations de base
    name: str = Field(max_length=100, index=True)
    description: Optional[str] = Field(max_length=500)
    sector_type: str = Field(max_length=50)  # operational, restricted, patrol, etc.
    
    # Géométrie (PostGIS)
    geometry: str = Field(
        sa_column=Column(Geometry("GEOMETRY", srid=4326))
    )
    
    # Propriétés
    area_km2: Optional[float] = Field(default=None)  # Surface en km²
    perimeter_km: Optional[float] = Field(default=None)  # Périmètre en km
    
    # Métadonnées
    properties: Dict[str, Any] = Field(default_factory=dict, sa_column_kwargs={"type_": "JSON"})
    style: Dict[str, Any] = Field(default_factory=dict, sa_column_kwargs={"type_": "JSON"})
    
    # Statut
    is_active: bool = Field(default=True)
    priority: int = Field(default=0)  # Priorité d'affichage
    
    # Timestamps
    created_at: datetime = Field(default_factory=datetime.utcnow)
    updated_at: Optional[datetime] = Field(default=None)
    
    # Créateur
    created_by_id: Optional[int] = Field(foreign_key="users.id")

class Zone(SQLModel, table=True):
    """Zones d'intérêt ou de restriction"""
    __tablename__ = "zones"
    
    id: Optional[int] = Field(default=None, primary_key=True)
    uuid: str = Field(default_factory=lambda: str(uuid.uuid4()), unique=True, index=True)
    
    # Informations de base
    name: str = Field(max_length=100, index=True)
    description: Optional[str] = Field(max_length=500)
    zone_type: str = Field(max_length=50)  # interest, restricted, danger, safe, etc.
    
    # Géométrie
    geometry: str = Field(
        sa_column=Column(Geometry("GEOMETRY", srid=4326))
    )
    
    # Rayon pour les zones circulaires (en mètres)
    radius_m: Optional[float] = Field(default=None)
    
    # Altitude (pour zones 3D)
    min_altitude: Optional[float] = Field(default=None)
    max_altitude: Optional[float] = Field(default=None)
    
    # Propriétés
    properties: Dict[str, Any] = Field(default_factory=dict, sa_column_kwargs={"type_": "JSON"})
    style: Dict[str, Any] = Field(default_factory=dict, sa_column_kwargs={"type_": "JSON"})
    
    # Statut
    is_active: bool = Field(default=True)
    is_visible: bool = Field(default=True)
    
    # Timestamps
    created_at: datetime = Field(default_factory=datetime.utcnow)
    updated_at: Optional[datetime] = Field(default=None)
    valid_from: Optional[datetime] = Field(default=None)
    valid_until: Optional[datetime] = Field(default=None)
    
    # Créateur
    created_by_id: Optional[int] = Field(foreign_key="users.id")

class Waypoint(SQLModel, table=True):
    """Points de navigation et marqueurs"""
    __tablename__ = "waypoints"
    
    id: Optional[int] = Field(default=None, primary_key=True)
    uuid: str = Field(default_factory=lambda: str(uuid.uuid4()), unique=True, index=True)
    
    # Informations de base
    name: str = Field(max_length=100, index=True)
    description: Optional[str] = Field(max_length=500)
    waypoint_type: str = Field(max_length=50)  # marker, target, reference, etc.
    
    # Position (PostGIS Point)
    location: str = Field(
        sa_column=Column(Geometry("POINT", srid=4326))
    )
    altitude: Optional[float] = Field(default=None)  # Altitude en mètres
    
    # Propriétés visuelles
    icon: Optional[str] = Field(max_length=50, default="marker")
    color: Optional[str] = Field(max_length=7, default="#2196F3")
    size: Optional[int] = Field(default=20)
    
    # Métadonnées
    properties: Dict[str, Any] = Field(default_factory=dict, sa_column_kwargs={"type_": "JSON"})
    
    # Statut
    is_active: bool = Field(default=True)
    is_visible: bool = Field(default=True)
    
    # Timestamps
    created_at: datetime = Field(default_factory=datetime.utcnow)
    updated_at: Optional[datetime] = Field(default=None)
    
    # Créateur
    created_by_id: Optional[int] = Field(foreign_key="users.id")

class Track(SQLModel, table=True):
    """Traces et trajectoires"""
    __tablename__ = "tracks"
    
    id: Optional[int] = Field(default=None, primary_key=True)
    uuid: str = Field(default_factory=lambda: str(uuid.uuid4()), unique=True, index=True)
    
    # Informations de base
    name: str = Field(max_length=100, index=True)
    description: Optional[str] = Field(max_length=500)
    track_type: str = Field(max_length=50)  # route, patrol, movement, etc.
    
    # Géométrie (LineString ou MultiLineString)
    geometry: str = Field(
        sa_column=Column(Geometry("LINESTRING", srid=4326))
    )
    
    # Propriétés
    total_distance_km: Optional[float] = Field(default=None)
    total_duration_minutes: Optional[int] = Field(default=None)
    
    # Style
    style: Dict[str, Any] = Field(default_factory=dict, sa_column_kwargs={"type_": "JSON"})
    
    # Statut
    is_active: bool = Field(default=True)
    is_visible: bool = Field(default=True)
    
    # Timestamps
    created_at: datetime = Field(default_factory=datetime.utcnow)
    updated_at: Optional[datetime] = Field(default=None)
    
    # Créateur
    created_by_id: Optional[int] = Field(foreign_key="users.id")

class Coverage(SQLModel, table=True):
    """Zones de couverture des équipements"""
    __tablename__ = "coverage"
    
    id: Optional[int] = Field(default=None, primary_key=True)
    equipment_id: int = Field(foreign_key="equipment.id")
    
    # Type de couverture
    coverage_type: str = Field(max_length=50)  # radio, optical, radar, etc.
    
    # Géométrie de couverture
    geometry: str = Field(
        sa_column=Column(Geometry("GEOMETRY", srid=4326))
    )
    
    # Paramètres de couverture
    frequency_mhz: Optional[float] = Field(default=None)
    power_dbm: Optional[float] = Field(default=None)
    range_km: Optional[float] = Field(default=None)
    azimuth_start: Optional[float] = Field(default=None)  # Degrés
    azimuth_end: Optional[float] = Field(default=None)    # Degrés
    elevation_min: Optional[float] = Field(default=None)  # Degrés
    elevation_max: Optional[float] = Field(default=None)  # Degrés
    
    # Propriétés
    properties: Dict[str, Any] = Field(default_factory=dict, sa_column_kwargs={"type_": "JSON"})
    
    # Statut
    is_active: bool = Field(default=True)
    confidence: Optional[float] = Field(default=1.0)  # Confiance 0-1
    
    # Timestamps
    created_at: datetime = Field(default_factory=datetime.utcnow)
    updated_at: Optional[datetime] = Field(default=None)
