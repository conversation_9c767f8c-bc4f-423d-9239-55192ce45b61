import time
import uuid
from typing import Callable
from fastapi import Request, Response
from fastapi.middleware.base import BaseHTTPMiddleware
import structlog

logger = structlog.get_logger()

class LoggingMiddleware(BaseHTTPMiddleware):
    """Middleware de logging des requêtes"""
    
    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        # <PERSON><PERSON><PERSON>rer un ID unique pour la requête
        request_id = str(uuid.uuid4())
        
        # Ajouter l'ID à la requête
        request.state.request_id = request_id
        
        # Logger le début de la requête
        start_time = time.time()
        
        logger.info(
            "Requête reçue",
            request_id=request_id,
            method=request.method,
            url=str(request.url),
            client_ip=request.client.host if request.client else None,
            user_agent=request.headers.get("user-agent"),
        )
        
        try:
            # Traiter la requête
            response = await call_next(request)
            
            # Calculer le temps de traitement
            process_time = time.time() - start_time
            
            # Logger la réponse
            logger.info(
                "Requête traitée",
                request_id=request_id,
                status_code=response.status_code,
                process_time=round(process_time, 4),
            )
            
            # Ajouter l'ID de requête aux en-têtes de réponse
            response.headers["X-Request-ID"] = request_id
            response.headers["X-Process-Time"] = str(round(process_time, 4))
            
            return response
            
        except Exception as e:
            # Logger les erreurs
            process_time = time.time() - start_time
            logger.error(
                "Erreur lors du traitement de la requête",
                request_id=request_id,
                error=str(e),
                process_time=round(process_time, 4),
                exc_info=True,
            )
            raise

class SecurityMiddleware(BaseHTTPMiddleware):
    """Middleware de sécurité"""
    
    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        response = await call_next(request)
        
        # Ajouter des en-têtes de sécurité
        response.headers["X-Content-Type-Options"] = "nosniff"
        response.headers["X-Frame-Options"] = "DENY"
        response.headers["X-XSS-Protection"] = "1; mode=block"
        response.headers["Referrer-Policy"] = "strict-origin-when-cross-origin"
        response.headers["Permissions-Policy"] = "geolocation=(), microphone=(), camera=()"
        
        # CSP pour les environnements de production
        if not request.url.path.startswith("/docs") and not request.url.path.startswith("/redoc"):
            response.headers["Content-Security-Policy"] = (
                "default-src 'self'; "
                "script-src 'self' 'unsafe-inline' 'unsafe-eval'; "
                "style-src 'self' 'unsafe-inline'; "
                "img-src 'self' data: https:; "
                "connect-src 'self' ws: wss:; "
                "font-src 'self'; "
                "object-src 'none'; "
                "base-uri 'self'; "
                "form-action 'self'"
            )
        
        return response

class RateLimitMiddleware(BaseHTTPMiddleware):
    """Middleware de limitation de taux (simple)"""
    
    def __init__(self, app, calls: int = 100, period: int = 60):
        super().__init__(app)
        self.calls = calls
        self.period = period
        self.clients = {}
    
    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        client_ip = request.client.host if request.client else "unknown"
        current_time = time.time()
        
        # Nettoyer les anciennes entrées
        self.clients = {
            ip: timestamps for ip, timestamps in self.clients.items()
            if any(t > current_time - self.period for t in timestamps)
        }
        
        # Vérifier le taux pour ce client
        if client_ip in self.clients:
            # Filtrer les timestamps récents
            recent_calls = [
                t for t in self.clients[client_ip]
                if t > current_time - self.period
            ]
            
            if len(recent_calls) >= self.calls:
                logger.warning(
                    "Limite de taux dépassée",
                    client_ip=client_ip,
                    calls=len(recent_calls),
                    limit=self.calls
                )
                return Response(
                    content="Trop de requêtes",
                    status_code=429,
                    headers={"Retry-After": str(self.period)}
                )
            
            self.clients[client_ip] = recent_calls + [current_time]
        else:
            self.clients[client_ip] = [current_time]
        
        return await call_next(request)
