import React, { useState, useEffect } from 'react';
import { Link, useLocation, useNavigate } from 'react-router-dom';
import { motion } from 'framer-motion';
import {
  User,
  Settings,
  LogOut,
  Shield,
  Clock,
  Wifi,
  WifiOff,
  ChevronDown,
  Map,
  BarChart3,
  Radio,
  Package,
  AlertTriangle
} from 'lucide-react';
import { useAuthStore } from '@/store/authStore';
import { useWebSocket } from '@/hooks/useWebSocket';
import { APP_NAME, APP_VERSION } from '@/utils/constants';

const Navbar = () => {
  const { user, logout } = useAuthStore();
  const { isConnected } = useWebSocket();
  const location = useLocation();
  const navigate = useNavigate();
  const [currentTime, setCurrentTime] = useState(new Date());
  const [showUserMenu, setShowUserMenu] = useState(false);
  const [operationalStatus, setOperationalStatus] = useState('OPERATIONAL');

  // Menu de navigation
  const navigationItems = [
    { path: '/map', label: 'Carte', icon: Map },
    { path: '/map-test', label: 'WebWorldWind', icon: Map, badge: 'TEST' },
    { path: '/dashboard', label: 'Dashboard', icon: BarChart3 },
    { path: '/equipment', label: 'Équipements', icon: Radio },
    { path: '/plugins', label: 'Plugins', icon: Package },
    { path: '/alerts', label: 'Alertes', icon: AlertTriangle },
    { path: '/settings', label: 'Paramètres', icon: Settings },
  ];

  // Mise à jour de l'horloge
  useEffect(() => {
    const timer = setInterval(() => {
      setCurrentTime(new Date());
    }, 1000);

    return () => clearInterval(timer);
  }, []);

  // Déterminer le statut opérationnel basé sur la connexion
  useEffect(() => {
    if (!isConnected) {
      setOperationalStatus('DEGRADED');
    } else {
      setOperationalStatus('OPERATIONAL');
    }
  }, [isConnected]);

  const handleLogout = () => {
    console.log('Déconnexion en cours...');
    setShowUserMenu(false);

    // Appeler la fonction logout du store
    logout();

    // Double sécurité : nettoyer manuellement
    localStorage.removeItem('auth-storage');
    sessionStorage.clear();

    // Redirection immédiate avec rechargement complet
    setTimeout(() => {
      window.location.href = '/login';
      window.location.reload();
    }, 100);
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'OPERATIONAL':
        return 'text-status-active';
      case 'DEGRADED':
        return 'text-status-standby';
      case 'OFFLINE':
        return 'text-status-offline';
      default:
        return 'text-status-unknown';
    }
  };

  const formatTime = (date) => {
    return date.toLocaleTimeString('fr-FR', {
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit',
      timeZone: 'Europe/Paris'
    });
  };

  const formatDate = (date) => {
    return date.toLocaleDateString('fr-FR', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric'
    });
  };

  return (
    <nav className="h-16 bg-c2-black border-b border-c2-gray-300 flex items-center justify-between px-4 relative z-50">
      {/* Logo et titre */}
      <div className="flex items-center space-x-8">
        <div className="flex items-center space-x-2">
          <Shield className="w-8 h-8 text-c2-blue" />
          <div>
            <h1 className="text-lg font-bold text-c2-white">{APP_NAME}</h1>
            <span className="text-xs text-c2-gray-100">v{APP_VERSION}</span>
          </div>
        </div>

        {/* Menu de navigation */}
        <div className="flex items-center space-x-1">
          {navigationItems.map((item) => {
            const Icon = item.icon;
            const isActive = location.pathname === item.path;

            return (
              <Link
                key={item.path}
                to={item.path}
                className={`relative flex items-center space-x-2 px-3 py-2 rounded-md text-sm font-medium transition-colors ${
                  isActive
                    ? 'bg-c2-blue text-white'
                    : 'text-c2-gray-100 hover:text-c2-white hover:bg-c2-gray-300'
                }`}
              >
                <Icon className="w-4 h-4" />
                <span>{item.label}</span>
                {item.badge && (
                  <span className="absolute -top-1 -right-1 bg-orange-500 text-white text-xs px-1.5 py-0.5 rounded-full font-bold">
                    {item.badge}
                  </span>
                )}
              </Link>
            );
          })}
        </div>
      </div>

      {/* Statut opérationnel et horodatage central */}
      <div className="flex items-center space-x-8">
        {/* Statut opérationnel */}
        <div className="flex items-center space-x-2">
          <div className={`w-3 h-3 rounded-full ${
            operationalStatus === 'OPERATIONAL' ? 'bg-status-active' :
            operationalStatus === 'DEGRADED' ? 'bg-status-standby' :
            'bg-status-offline'
          } pulse-status`} />
          <span className={`font-semibold ${getStatusColor(operationalStatus)}`}>
            {operationalStatus}
          </span>
        </div>

        {/* Connexion WebSocket */}
        <div className="flex items-center space-x-2">
          {isConnected ? (
            <Wifi className="w-4 h-4 text-status-active" />
          ) : (
            <WifiOff className="w-4 h-4 text-status-offline" />
          )}
          <span className={`text-sm ${isConnected ? 'text-status-active' : 'text-status-offline'}`}>
            {isConnected ? 'CONNECTÉ' : 'DÉCONNECTÉ'}
          </span>
        </div>

        {/* Horodatage */}
        <div className="flex items-center space-x-2">
          <Clock className="w-4 h-4 text-c2-gray-100" />
          <div className="text-center">
            <div className="text-sm font-mono text-c2-white">
              {formatTime(currentTime)}
            </div>
            <div className="text-xs text-c2-gray-100">
              {formatDate(currentTime)}
            </div>
          </div>
        </div>
      </div>

      {/* Profil utilisateur */}
      <div className="relative">
        <button
          onClick={() => setShowUserMenu(!showUserMenu)}
          className="flex items-center space-x-2 px-3 py-2 rounded-lg hover:bg-c2-gray-400 transition-colors"
        >
          <div className="w-8 h-8 bg-c2-blue rounded-full flex items-center justify-center">
            <User className="w-5 h-5 text-white" />
          </div>
          <div className="text-left hidden md:block">
            <div className="text-sm font-medium text-c2-white">
              {user?.full_name || user?.username}
            </div>
            <div className="text-xs text-c2-gray-100 uppercase">
              {user?.role}
            </div>
          </div>
          <ChevronDown className={`w-4 h-4 text-c2-gray-100 transition-transform ${
            showUserMenu ? 'rotate-180' : ''
          }`} />
        </button>

        {/* Menu déroulant utilisateur */}
        {showUserMenu && (
          <motion.div
            initial={{ opacity: 0, y: -10 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -10 }}
            className="absolute right-0 top-full mt-2 w-64 bg-c2-gray-300 border border-c2-gray-200 rounded-lg shadow-lg overflow-hidden"
          >
            {/* Informations utilisateur */}
            <div className="p-4 border-b border-c2-gray-200">
              <div className="flex items-center space-x-3">
                <div className="w-12 h-12 bg-c2-blue rounded-full flex items-center justify-center">
                  <User className="w-6 h-6 text-white" />
                </div>
                <div>
                  <div className="font-medium text-c2-white">
                    {user?.full_name}
                  </div>
                  <div className="text-sm text-c2-gray-100">
                    {user?.email}
                  </div>
                  <div className="text-xs text-c2-blue uppercase font-semibold">
                    {user?.role}
                  </div>
                </div>
              </div>
            </div>

            {/* Actions */}
            <div className="py-2">
              <button
                onClick={() => setShowUserMenu(false)}
                className="w-full px-4 py-2 text-left hover:bg-c2-gray-400 flex items-center space-x-2 text-c2-white"
              >
                <Settings className="w-4 h-4" />
                <span>Paramètres</span>
              </button>
              
              <button
                onClick={handleLogout}
                className="w-full px-4 py-2 text-left hover:bg-c2-gray-400 flex items-center space-x-2 text-status-offline"
              >
                <LogOut className="w-4 h-4" />
                <span>Déconnexion</span>
              </button>
            </div>
          </motion.div>
        )}
      </div>

      {/* Overlay pour fermer le menu */}
      {showUserMenu && (
        <div
          className="fixed inset-0 z-40"
          onClick={() => setShowUserMenu(false)}
        />
      )}
    </nav>
  );
};

export default Navbar;
