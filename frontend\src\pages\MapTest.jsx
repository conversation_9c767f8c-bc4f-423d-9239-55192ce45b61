import React, { useState } from 'react';
import { motion } from 'framer-motion';
import WebWorldWindMap from '../components/map/WebWorldWindMap';
import { ArrowLeft, Globe, Map, Info } from 'lucide-react';

const MapTest = () => {
  const [mapInstance, setMapInstance] = useState(null);
  const [clickInfo, setClickInfo] = useState(null);

  const handleMapReady = (wwd) => {
    console.log('Map ready:', wwd);
    setMapInstance(wwd);
  };

  const handleMapClick = (event) => {
    console.log('Map clicked:', event);
    setClickInfo({
      lat: event.lat.toFixed(6),
      lng: event.lng.toFixed(6),
      timestamp: new Date().toLocaleTimeString()
    });
  };

  const goBack = () => {
    window.history.back();
  };

  const zoomToMorocco = () => {
    if (mapInstance) {
      mapInstance.navigator.lookAtLocation.latitude = 31.7917;
      mapInstance.navigator.lookAtLocation.longitude = -7.0926;
      mapInstance.navigator.range = 1000000;
      mapInstance.redraw();
    }
  };

  const zoomToRegion = () => {
    if (mapInstance) {
      mapInstance.navigator.lookAtLocation.latitude = 28.0;
      mapInstance.navigator.lookAtLocation.longitude = -5.0;
      mapInstance.navigator.range = 3000000;
      mapInstance.redraw();
    }
  };

  return (
    <div className="w-full h-screen bg-c2-black flex flex-col">
      {/* Header */}
      <motion.div
        className="bg-c2-gray-400 border-b border-c2-gray-300 p-4 flex items-center justify-between"
        initial={{ y: -50, opacity: 0 }}
        animate={{ y: 0, opacity: 1 }}
        transition={{ duration: 0.3 }}
      >
        <div className="flex items-center space-x-4">
          <button
            onClick={goBack}
            className="flex items-center space-x-2 text-c2-gray-100 hover:text-c2-white transition-colors"
          >
            <ArrowLeft className="w-5 h-5" />
            <span>Retour</span>
          </button>

          <div className="h-6 w-px bg-c2-gray-300"></div>

          <div className="flex items-center space-x-2">
            <Globe className="w-6 h-6 text-c2-blue" />
            <h1 className="text-xl font-bold text-c2-white">
              Test WebWorldWind - Maroc 2D
            </h1>
          </div>
        </div>

        <div className="flex items-center space-x-2">
          <button
            onClick={zoomToMorocco}
            className="px-3 py-2 bg-c2-blue text-white rounded hover:bg-blue-600 transition-colors text-sm"
          >
            <Map className="w-4 h-4 inline mr-1" />
            Maroc
          </button>
          <button
            onClick={zoomToRegion}
            className="px-3 py-2 bg-c2-gray-300 text-c2-white rounded hover:bg-c2-gray-200 transition-colors text-sm"
          >
            <Globe className="w-4 h-4 inline mr-1" />
            Région
          </button>
        </div>
      </motion.div>

      {/* Map Container */}
      <div className="flex-1 relative">
        <WebWorldWindMap
          className="w-full h-full"
          onMapReady={handleMapReady}
          onMapClick={handleMapClick}
        />

        {/* Info Panel */}
        {clickInfo && (
          <motion.div
            className="absolute bottom-4 right-4 bg-c2-gray-400 border border-c2-gray-300 rounded-lg p-4 min-w-64"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.3 }}
          >
            <div className="flex items-center space-x-2 mb-3">
              <Info className="w-5 h-5 text-c2-blue" />
              <h3 className="font-semibold text-c2-white">Clic sur la carte</h3>
            </div>

            <div className="space-y-2 text-sm">
              <div className="flex justify-between">
                <span className="text-c2-gray-100">Latitude:</span>
                <span className="text-c2-white font-mono">{clickInfo.lat}°</span>
              </div>
              <div className="flex justify-between">
                <span className="text-c2-gray-100">Longitude:</span>
                <span className="text-c2-white font-mono">{clickInfo.lng}°</span>
              </div>
              <div className="flex justify-between">
                <span className="text-c2-gray-100">Heure:</span>
                <span className="text-c2-white">{clickInfo.timestamp}</span>
              </div>
            </div>

            <button
              onClick={() => setClickInfo(null)}
              className="mt-3 w-full px-3 py-1 bg-c2-gray-300 text-c2-white rounded hover:bg-c2-gray-200 transition-colors text-xs"
            >
              Fermer
            </button>
          </motion.div>
        )}

        {/* Instructions */}
        <motion.div
          className="absolute top-4 right-4 bg-c2-gray-400 bg-opacity-90 border border-c2-gray-300 rounded-lg p-4 max-w-sm"
          initial={{ opacity: 0, x: 20 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ duration: 0.3, delay: 0.5 }}
        >
          <h3 className="font-semibold text-c2-white mb-2 flex items-center">
            <Info className="w-4 h-4 mr-2 text-c2-blue" />
            Instructions
          </h3>
          <ul className="text-sm text-c2-gray-100 space-y-1">
            <li>• Cliquez pour obtenir les coordonnées</li>
            <li>• Glissez pour déplacer la vue</li>
            <li>• Molette pour zoomer</li>
            <li>• Utilisez les boutons pour naviguer</li>
          </ul>
        </motion.div>
      </div>

      {/* Status Bar */}
      <motion.div
        className="bg-c2-gray-400 border-t border-c2-gray-300 px-4 py-2 flex items-center justify-between text-sm"
        initial={{ y: 50, opacity: 0 }}
        animate={{ y: 0, opacity: 1 }}
        transition={{ duration: 0.3, delay: 0.2 }}
      >
        <div className="flex items-center space-x-4">
          <span className="text-c2-gray-100">
            Mode: <span className="text-c2-white font-semibold">2D</span>
          </span>
          <span className="text-c2-gray-100">
            Région: <span className="text-c2-white">Maroc + Voisins</span>
          </span>
        </div>

        <div className="flex items-center space-x-2">
          <div className="w-2 h-2 bg-green-400 rounded-full"></div>
          <span className="text-c2-gray-100">WebWorldWind Actif</span>
        </div>
      </motion.div>
    </div>
  );
};

export default MapTest;
