<!doctype html>
<html lang="fr">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/svg+xml" href="/favicon.ico" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>C2-EW Platform - Command & Control Electronic Warfare</title>
    <meta name="description" content="Plateforme C2-EW pour piloter et superviser des équipements de guerre électronique" />

    <!-- WebWorldWind Library -->
    <script src="https://files.worldwind.arc.nasa.gov/artifactory/web/0.11.0/worldwind.min.js" type="text/javascript"></script>


    <style>
      /* Critical CSS pour éviter le FOUC */
      body {
        margin: 0;
        padding: 0;
        background-color: #000000;
        color: #ffffff;
        font-family: 'Segoe UI', system-ui, sans-serif;
        overflow: hidden;
      }
      
      #root {
        width: 100vw;
        height: 100vh;
      }
      
      /* Loading spinner */
      .loading-spinner {
        position: fixed;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        width: 40px;
        height: 40px;
        border: 3px solid #333333;
        border-top: 3px solid #2196F3;
        border-radius: 50%;
        animation: spin 1s linear infinite;
      }
      
      @keyframes spin {
        0% { transform: translate(-50%, -50%) rotate(0deg); }
        100% { transform: translate(-50%, -50%) rotate(360deg); }
      }
    </style>
  </head>
  <body>
    <div id="root">
      <div class="loading-spinner"></div>
    </div>
    <script type="module" src="/src/main.jsx"></script>
  </body>
</html>
