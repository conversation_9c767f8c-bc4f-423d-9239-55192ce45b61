from enum import Enum
from typing import List, Optional
from fastapi import HTTPException, status, Depends
import structlog

from app.models.user import User
from app.core.security import get_current_user_token
from app.database import get_session
from sqlmodel import Session, select

logger = structlog.get_logger()

class Role(str, Enum):
    """Rôles utilisateur"""
    ADMIN = "admin"
    OPERATOR = "operator"
    VIEWER = "viewer"
    TECHNICIAN = "technician"

class Permission(str, Enum):
    """Permissions système"""
    # Équipements
    EQUIPMENT_READ = "equipment:read"
    EQUIPMENT_WRITE = "equipment:write"
    EQUIPMENT_CONTROL = "equipment:control"
    EQUIPMENT_DELETE = "equipment:delete"
    
    # Plugins
    PLUGIN_READ = "plugin:read"
    PLUGIN_INSTALL = "plugin:install"
    PLUGIN_CONFIGURE = "plugin:configure"
    PLUGIN_DELETE = "plugin:delete"
    
    # Utilisateurs
    USER_READ = "user:read"
    USER_WRITE = "user:write"
    USER_DELETE = "user:delete"
    
    # Système
    SYSTEM_CONFIG = "system:config"
    SYSTEM_LOGS = "system:logs"
    SYSTEM_MONITOR = "system:monitor"
    
    # Géospatial
    GEO_READ = "geo:read"
    GEO_WRITE = "geo:write"
    
    # Audio/Streaming
    AUDIO_STREAM = "audio:stream"
    AUDIO_RECORD = "audio:record"

# Mapping des rôles vers les permissions
ROLE_PERMISSIONS = {
    Role.ADMIN: [
        # Toutes les permissions
        Permission.EQUIPMENT_READ, Permission.EQUIPMENT_WRITE, 
        Permission.EQUIPMENT_CONTROL, Permission.EQUIPMENT_DELETE,
        Permission.PLUGIN_READ, Permission.PLUGIN_INSTALL,
        Permission.PLUGIN_CONFIGURE, Permission.PLUGIN_DELETE,
        Permission.USER_READ, Permission.USER_WRITE, Permission.USER_DELETE,
        Permission.SYSTEM_CONFIG, Permission.SYSTEM_LOGS, Permission.SYSTEM_MONITOR,
        Permission.GEO_READ, Permission.GEO_WRITE,
        Permission.AUDIO_STREAM, Permission.AUDIO_RECORD,
    ],
    Role.OPERATOR: [
        # Opérations sur équipements et lecture système
        Permission.EQUIPMENT_READ, Permission.EQUIPMENT_WRITE, Permission.EQUIPMENT_CONTROL,
        Permission.PLUGIN_READ, Permission.PLUGIN_CONFIGURE,
        Permission.USER_READ,
        Permission.SYSTEM_LOGS, Permission.SYSTEM_MONITOR,
        Permission.GEO_READ, Permission.GEO_WRITE,
        Permission.AUDIO_STREAM, Permission.AUDIO_RECORD,
    ],
    Role.TECHNICIAN: [
        # Maintenance et configuration technique
        Permission.EQUIPMENT_READ, Permission.EQUIPMENT_WRITE,
        Permission.PLUGIN_READ, Permission.PLUGIN_CONFIGURE,
        Permission.USER_READ,
        Permission.SYSTEM_LOGS, Permission.SYSTEM_MONITOR,
        Permission.GEO_READ,
        Permission.AUDIO_STREAM,
    ],
    Role.VIEWER: [
        # Lecture seule
        Permission.EQUIPMENT_READ,
        Permission.PLUGIN_READ,
        Permission.USER_READ,
        Permission.SYSTEM_MONITOR,
        Permission.GEO_READ,
    ],
}

def get_user_permissions(role: Role) -> List[Permission]:
    """Obtenir les permissions d'un rôle"""
    return ROLE_PERMISSIONS.get(role, [])

def has_permission(user_role: Role, required_permission: Permission) -> bool:
    """Vérifier si un rôle a une permission"""
    user_permissions = get_user_permissions(user_role)
    return required_permission in user_permissions

async def get_current_user(
    username: str = Depends(get_current_user_token),
    session: Session = Depends(get_session)
) -> User:
    """Obtenir l'utilisateur actuel"""
    statement = select(User).where(User.username == username)
    user = session.exec(statement).first()
    
    if not user:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Utilisateur non trouvé"
        )
    
    if not user.is_active:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Utilisateur inactif"
        )
    
    return user

def require_permission(permission: Permission):
    """Décorateur pour exiger une permission"""
    def permission_checker(current_user: User = Depends(get_current_user)) -> User:
        if not has_permission(Role(current_user.role), permission):
            logger.warning(
                "Accès refusé - permission insuffisante",
                user=current_user.username,
                role=current_user.role,
                required_permission=permission
            )
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail=f"Permission requise: {permission}"
            )
        return current_user
    
    return permission_checker

def require_role(required_role: Role):
    """Décorateur pour exiger un rôle minimum"""
    def role_checker(current_user: User = Depends(get_current_user)) -> User:
        user_role = Role(current_user.role)
        
        # Hiérarchie des rôles (admin > operator > technician > viewer)
        role_hierarchy = {
            Role.VIEWER: 0,
            Role.TECHNICIAN: 1,
            Role.OPERATOR: 2,
            Role.ADMIN: 3,
        }
        
        if role_hierarchy.get(user_role, 0) < role_hierarchy.get(required_role, 0):
            logger.warning(
                "Accès refusé - rôle insuffisant",
                user=current_user.username,
                user_role=user_role,
                required_role=required_role
            )
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail=f"Rôle requis: {required_role}"
            )
        return current_user
    
    return role_checker
