import time
import hashlib
import secrets
from typing import Optional
from fastapi import Request, Response, HTTPException
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from starlette.middleware.base import BaseHTTPMiddleware
import structlog

from app.core.config import settings
from app.core.security_manager import security_manager

logger = structlog.get_logger()

class SecurityMiddleware(BaseHTTPMiddleware):
    """Middleware de sécurité pour la plateforme C2-EW"""
    
    def __init__(self, app):
        super().__init__(app)
        self.rate_limiter = {}
        self.blocked_ips = set()
        
    async def dispatch(self, request: Request, call_next):
        start_time = time.time()
        client_ip = self.get_client_ip(request)
        
        # Vérifications de sécurité
        security_check = await self.perform_security_checks(request, client_ip)
        if security_check:
            return security_check
        
        # Traiter la requête
        response = await call_next(request)
        
        # Ajouter les en-têtes de sécurité
        self.add_security_headers(response)
        
        # Logger la requête
        process_time = time.time() - start_time
        await self.log_request(request, response, client_ip, process_time)
        
        return response
    
    def get_client_ip(self, request: Request) -> str:
        """Obtenir l'IP réelle du client"""
        # Vérifier les en-têtes de proxy
        forwarded_for = request.headers.get("X-Forwarded-For")
        if forwarded_for:
            return forwarded_for.split(",")[0].strip()
        
        real_ip = request.headers.get("X-Real-IP")
        if real_ip:
            return real_ip
        
        return request.client.host if request.client else "unknown"
    
    async def perform_security_checks(self, request: Request, client_ip: str) -> Optional[Response]:
        """Effectuer les vérifications de sécurité"""
        
        # 1. Vérifier si l'IP est bloquée
        if security_manager.is_ip_blocked(client_ip):
            logger.warning("Tentative d'accès depuis une IP bloquée", ip=client_ip)
            return Response(
                status_code=403,
                content="Accès refusé",
                headers={"Retry-After": "3600"}
            )
        
        # 2. Rate limiting
        if not await self.check_rate_limit(request, client_ip):
            logger.warning("Rate limit dépassé", ip=client_ip, path=request.url.path)
            return Response(
                status_code=429,
                content="Trop de requêtes",
                headers={"Retry-After": "900"}
            )
        
        # 3. Vérifier la taille de la requête
        content_length = request.headers.get("content-length")
        if content_length and int(content_length) > settings.MAX_REQUEST_SIZE:
            logger.warning("Requête trop volumineuse", ip=client_ip, size=content_length)
            return Response(status_code=413, content="Requête trop volumineuse")
        
        # 4. Vérifier les en-têtes suspects
        if self.has_suspicious_headers(request):
            logger.warning("En-têtes suspects détectés", ip=client_ip)
            return Response(status_code=400, content="Requête invalide")
        
        # 5. Vérifier les tentatives d'injection
        if await self.detect_injection_attempts(request):
            logger.warning("Tentative d'injection détectée", ip=client_ip)
            security_manager.record_failed_attempt(client_ip)
            return Response(status_code=400, content="Requête invalide")
        
        return None
    
    async def check_rate_limit(self, request: Request, client_ip: str) -> bool:
        """Vérifier les limites de taux"""
        path = request.url.path
        method = request.method
        
        # Limites différentes selon l'endpoint
        if path.startswith("/api/v1/auth/login"):
            return security_manager.check_rate_limit(
                f"login:{client_ip}", max_attempts=5, window_minutes=15
            )
        elif path.startswith("/api/v1/auth/"):
            return security_manager.check_rate_limit(
                f"auth:{client_ip}", max_attempts=20, window_minutes=15
            )
        elif method in ["POST", "PUT", "DELETE", "PATCH"]:
            return security_manager.check_rate_limit(
                f"write:{client_ip}", max_attempts=100, window_minutes=15
            )
        else:
            return security_manager.check_rate_limit(
                f"read:{client_ip}", max_attempts=200, window_minutes=15
            )
    
    def has_suspicious_headers(self, request: Request) -> bool:
        """Détecter les en-têtes suspects"""
        suspicious_patterns = [
            "script", "javascript", "vbscript", "onload", "onerror",
            "<script", "</script", "eval(", "alert(", "document.cookie"
        ]
        
        for header_name, header_value in request.headers.items():
            if isinstance(header_value, str):
                header_lower = header_value.lower()
                if any(pattern in header_lower for pattern in suspicious_patterns):
                    return True
        
        return False
    
    async def detect_injection_attempts(self, request: Request) -> bool:
        """Détecter les tentatives d'injection"""
        # Patterns d'injection SQL
        sql_patterns = [
            "union select", "drop table", "insert into", "delete from",
            "update set", "exec(", "execute(", "sp_", "xp_", "'; --",
            "' or '1'='1", "' or 1=1", "admin'--", "' union select"
        ]
        
        # Patterns d'injection XSS
        xss_patterns = [
            "<script", "</script", "javascript:", "vbscript:", "onload=",
            "onerror=", "onclick=", "onmouseover=", "eval(", "alert(",
            "document.cookie", "window.location", "innerHTML"
        ]
        
        # Patterns d'injection de commandes
        cmd_patterns = [
            "; cat ", "; ls ", "; rm ", "; wget ", "; curl ", "| nc ",
            "&& cat", "&& ls", "$(cat", "`cat", "system(", "exec(",
            "passthru(", "shell_exec(", "popen("
        ]
        
        all_patterns = sql_patterns + xss_patterns + cmd_patterns
        
        # Vérifier l'URL
        url_str = str(request.url).lower()
        if any(pattern in url_str for pattern in all_patterns):
            return True
        
        # Vérifier les paramètres de requête
        for param_name, param_value in request.query_params.items():
            if isinstance(param_value, str):
                param_lower = param_value.lower()
                if any(pattern in param_lower for pattern in all_patterns):
                    return True
        
        # Vérifier le corps de la requête (si applicable)
        if request.method in ["POST", "PUT", "PATCH"]:
            try:
                body = await request.body()
                if body:
                    body_str = body.decode('utf-8', errors='ignore').lower()
                    if any(pattern in body_str for pattern in all_patterns):
                        return True
            except Exception:
                # Si on ne peut pas lire le corps, on continue
                pass
        
        return False
    
    def add_security_headers(self, response: Response):
        """Ajouter les en-têtes de sécurité"""
        security_headers = {
            "X-Content-Type-Options": "nosniff",
            "X-Frame-Options": "DENY",
            "X-XSS-Protection": "1; mode=block",
            "Referrer-Policy": "strict-origin-when-cross-origin",
            "Permissions-Policy": "geolocation=(), microphone=(), camera=()",
            "Cache-Control": "no-store, no-cache, must-revalidate, proxy-revalidate",
            "Pragma": "no-cache",
            "Expires": "0"
        }
        
        # HSTS seulement en HTTPS
        if settings.ENVIRONMENT == "production":
            security_headers["Strict-Transport-Security"] = "max-age=31536000; includeSubDomains; preload"
        
        # CSP adapté à l'application
        csp_policy = (
            "default-src 'self'; "
            "script-src 'self' 'unsafe-inline' 'unsafe-eval'; "
            "style-src 'self' 'unsafe-inline'; "
            "img-src 'self' data: https:; "
            "font-src 'self' data:; "
            "connect-src 'self' ws: wss:; "
            "frame-ancestors 'none'; "
            "base-uri 'self'; "
            "form-action 'self'"
        )
        security_headers["Content-Security-Policy"] = csp_policy
        
        for header, value in security_headers.items():
            response.headers[header] = value
    
    async def log_request(self, request: Request, response: Response, client_ip: str, process_time: float):
        """Logger les requêtes"""
        # Logger les requêtes sensibles
        sensitive_paths = ["/api/v1/auth/", "/api/v1/admin/", "/api/v1/commands/"]
        is_sensitive = any(request.url.path.startswith(path) for path in sensitive_paths)
        
        log_data = {
            "method": request.method,
            "path": request.url.path,
            "status_code": response.status_code,
            "client_ip": client_ip,
            "process_time": round(process_time, 4),
            "user_agent": request.headers.get("user-agent", ""),
            "referer": request.headers.get("referer", ""),
        }
        
        if is_sensitive or response.status_code >= 400:
            logger.info("Requête HTTP", **log_data)
        
        # Logger les erreurs
        if response.status_code >= 500:
            logger.error("Erreur serveur", **log_data)
        elif response.status_code >= 400:
            logger.warning("Erreur client", **log_data)

class CSRFProtection:
    """Protection CSRF pour les formulaires"""
    
    def __init__(self):
        self.tokens = {}
    
    def generate_token(self, session_id: str) -> str:
        """Générer un token CSRF"""
        token = secrets.token_urlsafe(32)
        self.tokens[session_id] = {
            'token': token,
            'created_at': time.time()
        }
        return token
    
    def validate_token(self, session_id: str, token: str) -> bool:
        """Valider un token CSRF"""
        if session_id not in self.tokens:
            return False
        
        stored_data = self.tokens[session_id]
        
        # Vérifier l'expiration (1 heure)
        if time.time() - stored_data['created_at'] > 3600:
            del self.tokens[session_id]
            return False
        
        return stored_data['token'] == token
    
    def cleanup_expired_tokens(self):
        """Nettoyer les tokens expirés"""
        current_time = time.time()
        expired_sessions = [
            session_id for session_id, data in self.tokens.items()
            if current_time - data['created_at'] > 3600
        ]
        
        for session_id in expired_sessions:
            del self.tokens[session_id]

# Instances globales
csrf_protection = CSRFProtection()
