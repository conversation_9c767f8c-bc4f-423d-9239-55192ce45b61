from fastapi import <PERSON><PERSON><PERSON>, WebSocket, WebSocketDisconnect
from fastapi.middleware.cors import CORSMiddleware
from fastapi.middleware.trustedhost import TrustedHostMiddleware
from fastapi.staticfiles import StaticFiles
import structlog

from app.config import settings
from app.core.middleware import LoggingMiddleware, SecurityMiddleware
from app.routers import auth, users, equipment, plugins, commands, alerts, geospatial, websocket
from app.database import engine
from app.models import *  # Import tous les modèles pour SQLModel

# Configuration du logging
structlog.configure(
    processors=[
        structlog.stdlib.filter_by_level,
        structlog.stdlib.add_logger_name,
        structlog.stdlib.add_log_level,
        structlog.stdlib.PositionalArgumentsFormatter(),
        structlog.processors.TimeStamper(fmt="iso"),
        structlog.processors.StackInfoRenderer(),
        structlog.processors.format_exc_info,
        structlog.processors.UnicodeDecoder(),
        structlog.processors.JSONRenderer()
    ],
    context_class=dict,
    logger_factory=structlog.stdlib.LoggerFactory(),
    cache_logger_on_first_use=True,
)

logger = structlog.get_logger()

# Création de l'application FastAPI
app = FastAPI(
    title=settings.PROJECT_NAME,
    description=settings.DESCRIPTION,
    version=settings.VERSION,
    openapi_url=f"{settings.API_V1_STR}/openapi.json",
    docs_url=f"{settings.API_V1_STR}/docs",
    redoc_url=f"{settings.API_V1_STR}/redoc",
)

# Middleware CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=settings.BACKEND_CORS_ORIGINS,
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Middleware de sécurité
app.add_middleware(TrustedHostMiddleware, allowed_hosts=["*"])
app.add_middleware(SecurityMiddleware)
app.add_middleware(LoggingMiddleware)

# Routes API
app.include_router(auth.router, prefix=f"{settings.API_V1_STR}/auth", tags=["auth"])
app.include_router(users.router, prefix=f"{settings.API_V1_STR}/users", tags=["users"])
app.include_router(equipment.router, prefix=f"{settings.API_V1_STR}/equipment", tags=["equipment"])
app.include_router(plugins.router, prefix=f"{settings.API_V1_STR}/plugins", tags=["plugins"])
app.include_router(commands.router, prefix=f"{settings.API_V1_STR}/commands", tags=["commands"])
app.include_router(alerts.router, prefix=f"{settings.API_V1_STR}/alerts", tags=["alerts"])
app.include_router(geospatial.router, prefix=f"{settings.API_V1_STR}/geo", tags=["geospatial"])
app.include_router(websocket.router, prefix="/ws", tags=["websocket"])

# Servir les fichiers statiques des plugins
app.mount("/plugins", StaticFiles(directory=settings.PLUGINS_DIR), name="plugins")

@app.on_event("startup")
async def startup_event():
    """Événements au démarrage de l'application"""
    logger.info("Démarrage de l'application C2-EW", version=settings.VERSION)
    
    # Créer les tables si elles n'existent pas
    # SQLModel.metadata.create_all(bind=engine)
    
    # Initialiser les plugins
    if settings.PLUGINS_ENABLED:
        from app.services.plugin_service import PluginService
        plugin_service = PluginService()
        await plugin_service.load_all_plugins()
        logger.info("Plugins chargés", count=len(plugin_service.loaded_plugins))

@app.on_event("shutdown")
async def shutdown_event():
    """Événements à l'arrêt de l'application"""
    logger.info("Arrêt de l'application C2-EW")

@app.get("/")
async def root():
    """Point d'entrée racine"""
    return {
        "message": "C2-EW Platform API",
        "version": settings.VERSION,
        "docs": f"{settings.API_V1_STR}/docs"
    }

@app.get("/health")
async def health_check():
    """Vérification de santé de l'API"""
    return {
        "status": "healthy",
        "version": settings.VERSION,
        "environment": settings.ENVIRONMENT
    }

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(
        "main:app",
        host="0.0.0.0",
        port=8000,
        reload=settings.DEBUG,
        log_level=settings.LOG_LEVEL.lower()
    )
