<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🗺️ Carte Offline Simple - Maroc</title>
    <style>
        body {
            margin: 0;
            padding: 0;
            background: #1a1a1a;
            color: white;
            font-family: Arial, sans-serif;
        }
        
        .container {
            width: 100vw;
            height: 100vh;
            display: flex;
            flex-direction: column;
        }
        
        .header {
            background: #2d5a27;
            padding: 15px;
            text-align: center;
        }
        
        .map-container {
            flex: 1;
            position: relative;
            background: #0a0a0a;
        }
        
        #mapCanvas {
            width: 100%;
            height: 100%;
            display: block;
            background: #87CEEB; /* <PERSON><PERSON><PERSON> océan */
        }
        
        .controls {
            position: absolute;
            top: 20px;
            right: 20px;
            background: rgba(0,0,0,0.9);
            padding: 20px;
            border-radius: 10px;
            min-width: 250px;
        }
        
        .btn {
            background: #4CAF50;
            color: white;
            border: none;
            padding: 10px 16px;
            margin: 5px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 14px;
            width: 100%;
        }
        
        .btn:hover {
            background: #45a049;
        }
        
        .status {
            position: absolute;
            bottom: 20px;
            left: 20px;
            background: rgba(0,0,0,0.8);
            padding: 15px;
            border-radius: 10px;
            font-size: 14px;
        }
        
        .offline-badge {
            background: #4CAF50;
            color: white;
            padding: 5px 10px;
            border-radius: 15px;
            font-size: 12px;
            font-weight: bold;
            position: absolute;
            top: 20px;
            left: 20px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🗺️ Carte Offline du Maroc - Version Simple</h1>
            <p>Carte fonctionnelle sans WebGL, utilisant Canvas 2D</p>
        </div>
        
        <div class="map-container">
            <canvas id="mapCanvas" width="1200" height="800">
                Votre navigateur ne supporte pas HTML5 Canvas.
            </canvas>
            
            <div class="offline-badge">
                📡 MODE OFFLINE
            </div>
            
            <div class="controls">
                <h3>🎛️ Contrôles</h3>
                
                <button class="btn" onclick="zoomIn()">🔍 Zoom +</button>
                <button class="btn" onclick="zoomOut()">🔍 Zoom -</button>
                
                <button class="btn" onclick="centerMorocco()">🇲🇦 Centrer Maroc</button>
                <button class="btn" onclick="showCities()">🏙️ Afficher Villes</button>
                
                <button class="btn" onclick="downloadTiles()">📥 Télécharger Tiles</button>
                <button class="btn" onclick="testOffline()">🔌 Test Offline</button>
                
                <div style="margin-top: 15px; font-size: 12px;">
                    <div>Zoom: <span id="zoomLevel">6</span></div>
                    <div>Position: <span id="position">31.79°, -7.09°</span></div>
                    <div>Tiles: <span id="tilesCount">0</span></div>
                </div>
            </div>
            
            <div class="status">
                <div>🗺️ Carte Canvas 2D - Fonctionne sans WebGL</div>
                <div>📍 Zone: Maroc + Régions voisines</div>
                <div>💾 Cache: <span id="cacheStatus">Vide</span></div>
            </div>
        </div>
    </div>

    <script>
        let canvas, ctx;
        let zoom = 6;
        let centerLat = 31.7917;
        let centerLng = -7.0926;
        let tilesLoaded = 0;
        
        // Données géographiques du Maroc
        const moroccoData = {
            outline: [
                {lat: 35.7, lng: -5.8},  // Tanger
                {lat: 35.2, lng: -2.9},  // Oujda
                {lat: 32.3, lng: -1.9},  // Frontière algérienne
                {lat: 27.7, lng: -1.0},  // Sud-est
                {lat: 27.7, lng: -8.7},  // Frontière mauritanienne
                {lat: 28.0, lng: -11.0}, // Sud-ouest
                {lat: 30.4, lng: -9.6},  // Agadir
                {lat: 33.6, lng: -7.6},  // Casablanca
                {lat: 34.0, lng: -6.8},  // Rabat
                {lat: 35.7, lng: -5.8}   // Retour Tanger
            ],
            cities: [
                {name: "Rabat", lat: 34.0209, lng: -6.8416, capital: true},
                {name: "Casablanca", lat: 33.5731, lng: -7.5898},
                {name: "Marrakech", lat: 31.2001, lng: -7.9218},
                {name: "Fès", lat: 34.0181, lng: -4.9998},
                {name: "Tanger", lat: 35.7595, lng: -5.8340},
                {name: "Agadir", lat: 30.4278, lng: -9.5981},
                {name: "Oujda", lat: 34.6814, lng: -1.9086},
                {name: "Tétouan", lat: 35.5889, lng: -5.3626}
            ]
        };
        
        function init() {
            canvas = document.getElementById('mapCanvas');
            ctx = canvas.getContext('2d');
            
            // Ajuster la taille du canvas
            resizeCanvas();
            window.addEventListener('resize', resizeCanvas);
            
            // Dessiner la carte initiale
            drawMap();
            
            // Simuler le chargement de tiles
            simulateTileLoading();
            
            console.log('✅ Carte Canvas 2D initialisée');
        }
        
        function resizeCanvas() {
            const container = canvas.parentElement;
            canvas.width = container.clientWidth;
            canvas.height = container.clientHeight;
            drawMap();
        }
        
        function drawMap() {
            // Effacer le canvas
            ctx.clearRect(0, 0, canvas.width, canvas.height);
            
            // Fond océan
            ctx.fillStyle = '#4682B4';
            ctx.fillRect(0, 0, canvas.width, canvas.height);
            
            // Dessiner le Maroc
            drawMorocco();
            
            // Dessiner les villes
            drawCities();
            
            // Dessiner la grille de tiles (simulation)
            drawTileGrid();
            
            // Mettre à jour les infos
            updateInfo();
        }
        
        function drawMorocco() {
            ctx.beginPath();
            ctx.fillStyle = '#D2B48C'; // Beige terre
            ctx.strokeStyle = '#8B4513'; // Marron frontières
            ctx.lineWidth = 2;
            
            const outline = moroccoData.outline;
            const firstPoint = latLngToPixel(outline[0].lat, outline[0].lng);
            ctx.moveTo(firstPoint.x, firstPoint.y);
            
            for (let i = 1; i < outline.length; i++) {
                const point = latLngToPixel(outline[i].lat, outline[i].lng);
                ctx.lineTo(point.x, point.y);
            }
            
            ctx.closePath();
            ctx.fill();
            ctx.stroke();
            
            // Ajouter du relief (simulation)
            drawMountains();
        }
        
        function drawMountains() {
            // Atlas Mountains (simulation)
            ctx.fillStyle = '#8B7355';
            ctx.beginPath();
            
            // Haut Atlas
            let point = latLngToPixel(31.5, -7.5);
            ctx.moveTo(point.x, point.y);
            point = latLngToPixel(31.8, -6.5);
            ctx.lineTo(point.x, point.y);
            point = latLngToPixel(31.3, -5.5);
            ctx.lineTo(point.x, point.y);
            point = latLngToPixel(31.0, -6.5);
            ctx.lineTo(point.x, point.y);
            
            ctx.closePath();
            ctx.fill();
        }
        
        function drawCities() {
            moroccoData.cities.forEach(city => {
                const point = latLngToPixel(city.lat, city.lng);
                
                // Point de la ville
                ctx.beginPath();
                ctx.fillStyle = city.capital ? '#FF0000' : '#000000';
                ctx.arc(point.x, point.y, city.capital ? 8 : 5, 0, 2 * Math.PI);
                ctx.fill();
                
                // Nom de la ville
                ctx.fillStyle = '#000000';
                ctx.font = city.capital ? 'bold 14px Arial' : '12px Arial';
                ctx.textAlign = 'left';
                ctx.fillText(city.name, point.x + 10, point.y - 5);
            });
        }
        
        function drawTileGrid() {
            // Dessiner une grille simulant les tiles
            ctx.strokeStyle = 'rgba(255, 255, 255, 0.3)';
            ctx.lineWidth = 1;
            
            const tileSize = 256 / Math.pow(2, zoom - 6); // Ajuster selon le zoom
            
            for (let x = 0; x < canvas.width; x += tileSize) {
                ctx.beginPath();
                ctx.moveTo(x, 0);
                ctx.lineTo(x, canvas.height);
                ctx.stroke();
            }
            
            for (let y = 0; y < canvas.height; y += tileSize) {
                ctx.beginPath();
                ctx.moveTo(0, y);
                ctx.lineTo(canvas.width, y);
                ctx.stroke();
            }
        }
        
        function latLngToPixel(lat, lng) {
            // Conversion simple lat/lng vers pixels
            const bounds = {
                north: 36, south: 20,
                east: 2, west: -18
            };
            
            const x = ((lng - bounds.west) / (bounds.east - bounds.west)) * canvas.width;
            const y = ((bounds.north - lat) / (bounds.north - bounds.south)) * canvas.height;
            
            return { x, y };
        }
        
        function updateInfo() {
            document.getElementById('zoomLevel').textContent = zoom;
            document.getElementById('position').textContent = `${centerLat.toFixed(2)}°, ${centerLng.toFixed(2)}°`;
            document.getElementById('tilesCount').textContent = tilesLoaded;
        }
        
        function zoomIn() {
            if (zoom < 12) {
                zoom++;
                drawMap();
                console.log(`🔍 Zoom in: ${zoom}`);
            }
        }
        
        function zoomOut() {
            if (zoom > 3) {
                zoom--;
                drawMap();
                console.log(`🔍 Zoom out: ${zoom}`);
            }
        }
        
        function centerMorocco() {
            centerLat = 31.7917;
            centerLng = -7.0926;
            zoom = 6;
            drawMap();
            console.log('🇲🇦 Centré sur le Maroc');
        }
        
        function showCities() {
            drawMap(); // Redessiner avec les villes
            console.log('🏙️ Villes affichées');
        }
        
        function downloadTiles() {
            console.log('📥 Simulation téléchargement tiles...');
            
            // Simuler le téléchargement
            let downloaded = 0;
            const total = 50;
            
            const interval = setInterval(() => {
                downloaded += Math.floor(Math.random() * 5) + 1;
                tilesLoaded = downloaded;
                
                if (downloaded >= total) {
                    downloaded = total;
                    clearInterval(interval);
                    document.getElementById('cacheStatus').textContent = 'Prêt offline';
                    console.log('✅ Téléchargement terminé');
                }
                
                updateInfo();
                document.getElementById('cacheStatus').textContent = `${downloaded}/${total} tiles`;
            }, 200);
        }
        
        function testOffline() {
            console.log('🔌 Test mode offline');
            alert('Cette carte fonctionne déjà offline !\n\n' +
                  '✅ Canvas 2D (pas de WebGL requis)\n' +
                  '✅ Données géographiques intégrées\n' +
                  '✅ Pas de dépendance internet\n\n' +
                  'Vous pouvez déconnecter internet et la carte continuera de fonctionner !');
        }
        
        function simulateTileLoading() {
            // Simuler le chargement progressif
            setTimeout(() => {
                tilesLoaded = 25;
                updateInfo();
                document.getElementById('cacheStatus').textContent = 'Chargement...';
            }, 1000);
        }
        
        // Initialisation au chargement
        window.addEventListener('load', init);
        
        // Gestion des clics sur la carte
        canvas.addEventListener('click', function(event) {
            const rect = canvas.getBoundingClientRect();
            const x = event.clientX - rect.left;
            const y = event.clientY - rect.top;
            
            console.log(`Clic sur la carte: ${x}, ${y}`);
            
            // Convertir en lat/lng approximatif
            const bounds = {
                north: 36, south: 20,
                east: 2, west: -18
            };
            
            const lng = bounds.west + (x / canvas.width) * (bounds.east - bounds.west);
            const lat = bounds.north - (y / canvas.height) * (bounds.north - bounds.south);
            
            console.log(`Position approximative: ${lat.toFixed(2)}°, ${lng.toFixed(2)}°`);
        });
    </script>
</body>
</html>
