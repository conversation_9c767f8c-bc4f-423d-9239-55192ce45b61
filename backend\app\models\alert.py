from sqlmodel import SQLModel, Field, Relationship
from typing import Optional, Dict, Any
from datetime import datetime
from enum import Enum
import uuid

class AlertLevel(str, Enum):
    INFO = "info"
    WARNING = "warning"
    ERROR = "error"
    CRITICAL = "critical"

class AlertType(str, Enum):
    SYSTEM = "system"
    EQUIPMENT = "equipment"
    SECURITY = "security"
    COMMUNICATION = "communication"
    OPERATIONAL = "operational"

class AlertStatus(str, Enum):
    ACTIVE = "active"
    ACKNOWLEDGED = "acknowledged"
    RESOLVED = "resolved"
    SUPPRESSED = "suppressed"

class Alert(SQLModel, table=True):
    """Modèle d'alerte système"""
    __tablename__ = "alerts"
    
    id: Optional[int] = Field(default=None, primary_key=True)
    uuid: str = Field(default_factory=lambda: str(uuid.uuid4()), unique=True, index=True)
    
    # Informations de base
    title: str = Field(max_length=200, index=True)
    message: str = Field(max_length=1000)
    description: Optional[str] = Field(max_length=2000)
    
    # Classification
    level: AlertLevel = Field(index=True)
    alert_type: AlertType = Field(index=True)
    category: Optional[str] = Field(max_length=50)  # Sous-catégorie
    
    # Source
    source: str = Field(max_length=100)  # system, equipment, plugin, user
    source_id: Optional[str] = Field(max_length=100)  # ID de la source
    
    # Équipement associé (optionnel)
    equipment_id: Optional[int] = Field(foreign_key="equipment.id")
    equipment: Optional["Equipment"] = Relationship(back_populates="alerts")
    
    # Statut
    status: AlertStatus = Field(default=AlertStatus.ACTIVE, index=True)
    
    # Données contextuelles
    context_data: Dict[str, Any] = Field(default_factory=dict, sa_column_kwargs={"type_": "JSON"})
    
    # Actions recommandées
    recommended_actions: list[str] = Field(default_factory=list, sa_column_kwargs={"type_": "JSON"})
    
    # Timestamps
    created_at: datetime = Field(default_factory=datetime.utcnow, index=True)
    updated_at: Optional[datetime] = Field(default=None)
    acknowledged_at: Optional[datetime] = Field(default=None)
    resolved_at: Optional[datetime] = Field(default=None)
    
    # Utilisateurs
    created_by_id: Optional[int] = Field(foreign_key="users.id")
    created_by: Optional["User"] = Relationship(back_populates="alerts_created")
    acknowledged_by_id: Optional[int] = Field(foreign_key="users.id")
    resolved_by_id: Optional[int] = Field(foreign_key="users.id")
    
    # Métadonnées
    is_persistent: bool = Field(default=False)  # L'alerte persiste après redémarrage
    auto_resolve: bool = Field(default=False)   # Résolution automatique possible
    notification_sent: bool = Field(default=False)

class AlertRule(SQLModel, table=True):
    """Règles de génération d'alertes"""
    __tablename__ = "alert_rules"
    
    id: Optional[int] = Field(default=None, primary_key=True)
    uuid: str = Field(default_factory=lambda: str(uuid.uuid4()), unique=True, index=True)
    
    # Informations de base
    name: str = Field(max_length=100, unique=True, index=True)
    description: Optional[str] = Field(max_length=500)
    
    # Condition de déclenchement
    condition: Dict[str, Any] = Field(sa_column_kwargs={"type_": "JSON"})  # Condition JSON
    
    # Configuration de l'alerte
    alert_template: Dict[str, Any] = Field(sa_column_kwargs={"type_": "JSON"})
    
    # Paramètres
    is_enabled: bool = Field(default=True)
    priority: int = Field(default=0)
    
    # Limitation de fréquence
    cooldown_minutes: int = Field(default=5)  # Temps minimum entre alertes
    max_alerts_per_hour: int = Field(default=10)
    
    # Timestamps
    created_at: datetime = Field(default_factory=datetime.utcnow)
    updated_at: Optional[datetime] = Field(default=None)
    last_triggered: Optional[datetime] = Field(default=None)
    
    # Créateur
    created_by_id: int = Field(foreign_key="users.id")

class AlertNotification(SQLModel, table=True):
    """Notifications d'alertes"""
    __tablename__ = "alert_notifications"
    
    id: Optional[int] = Field(default=None, primary_key=True)
    alert_id: int = Field(foreign_key="alerts.id")
    
    # Type de notification
    notification_type: str = Field(max_length=50)  # email, sms, webhook, websocket
    
    # Destinataire
    recipient: str = Field(max_length=255)  # email, phone, url, user_id
    
    # Statut
    status: str = Field(max_length=20, default="pending")  # pending, sent, failed
    
    # Contenu
    subject: Optional[str] = Field(max_length=200)
    content: str = Field(max_length=2000)
    
    # Métadonnées
    metadata: Dict[str, Any] = Field(default_factory=dict, sa_column_kwargs={"type_": "JSON"})
    
    # Timestamps
    created_at: datetime = Field(default_factory=datetime.utcnow)
    sent_at: Optional[datetime] = Field(default=None)
    
    # Retry logic
    retry_count: int = Field(default=0)
    max_retries: int = Field(default=3)
    error_message: Optional[str] = Field(max_length=500)

class AlertSubscription(SQLModel, table=True):
    """Abonnements aux alertes par utilisateur"""
    __tablename__ = "alert_subscriptions"
    
    id: Optional[int] = Field(default=None, primary_key=True)
    user_id: int = Field(foreign_key="users.id")
    
    # Filtres d'abonnement
    alert_levels: list[AlertLevel] = Field(sa_column_kwargs={"type_": "JSON"})
    alert_types: list[AlertType] = Field(sa_column_kwargs={"type_": "JSON"})
    equipment_ids: Optional[list[int]] = Field(default=None, sa_column_kwargs={"type_": "JSON"})
    
    # Méthodes de notification
    email_enabled: bool = Field(default=True)
    websocket_enabled: bool = Field(default=True)
    
    # Configuration
    is_active: bool = Field(default=True)
    
    # Timestamps
    created_at: datetime = Field(default_factory=datetime.utcnow)
    updated_at: Optional[datetime] = Field(default=None)
