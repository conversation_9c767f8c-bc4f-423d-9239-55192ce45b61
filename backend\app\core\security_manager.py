import secrets
import hashlib
import hmac
from datetime import datetime, timedelta
from typing import Optional, Dict, Any
import structlog

from app.core.config import settings

logger = structlog.get_logger()

class SecurityManager:
    """Gestionnaire de sécurité avancé pour la plateforme C2-EW"""
    
    def __init__(self):
        self.failed_attempts: Dict[str, list] = {}
        self.blocked_ips: Dict[str, datetime] = {}
        self.active_sessions: Dict[str, dict] = {}
        self.api_keys: Dict[str, dict] = {}
        
    def check_rate_limit(self, identifier: str, max_attempts: int = 5, window_minutes: int = 15) -> bool:
        """Vérifier les limites de taux (rate limiting)"""
        now = datetime.utcnow()
        window_start = now - timedelta(minutes=window_minutes)
        
        # Nettoyer les anciennes tentatives
        if identifier in self.failed_attempts:
            self.failed_attempts[identifier] = [
                attempt for attempt in self.failed_attempts[identifier]
                if attempt > window_start
            ]
        
        # Vérifier le nombre de tentatives
        attempts = len(self.failed_attempts.get(identifier, []))
        return attempts < max_attempts
    
    def record_failed_attempt(self, identifier: str):
        """Enregistrer une tentative échouée"""
        if identifier not in self.failed_attempts:
            self.failed_attempts[identifier] = []
        self.failed_attempts[identifier].append(datetime.utcnow())
        
        logger.warning("Tentative de connexion échouée", identifier=identifier)
    
    def is_ip_blocked(self, ip_address: str) -> bool:
        """Vérifier si une IP est bloquée"""
        if ip_address in self.blocked_ips:
            block_time = self.blocked_ips[ip_address]
            if datetime.utcnow() - block_time < timedelta(hours=1):
                return True
            else:
                # Débloquer l'IP après 1 heure
                del self.blocked_ips[ip_address]
        return False
    
    def block_ip(self, ip_address: str):
        """Bloquer une IP"""
        self.blocked_ips[ip_address] = datetime.utcnow()
        logger.warning("IP bloquée", ip_address=ip_address)
    
    def create_session(self, user_id: int, ip_address: str, user_agent: str) -> str:
        """Créer une session utilisateur"""
        session_id = secrets.token_urlsafe(32)
        self.active_sessions[session_id] = {
            'user_id': user_id,
            'ip_address': ip_address,
            'user_agent': user_agent,
            'created_at': datetime.utcnow(),
            'last_activity': datetime.utcnow()
        }
        return session_id
    
    def validate_session(self, session_id: str, ip_address: str, user_agent: str) -> Optional[dict]:
        """Valider une session"""
        if session_id not in self.active_sessions:
            return None
        
        session = self.active_sessions[session_id]
        
        # Vérifier l'IP et le user agent pour détecter le vol de session
        if session['ip_address'] != ip_address or session['user_agent'] != user_agent:
            logger.warning("Tentative de vol de session détectée", 
                         session_id=session_id, 
                         original_ip=session['ip_address'],
                         current_ip=ip_address)
            self.invalidate_session(session_id)
            return None
        
        # Vérifier l'expiration (24 heures d'inactivité)
        if datetime.utcnow() - session['last_activity'] > timedelta(hours=24):
            self.invalidate_session(session_id)
            return None
        
        # Mettre à jour l'activité
        session['last_activity'] = datetime.utcnow()
        return session
    
    def invalidate_session(self, session_id: str):
        """Invalider une session"""
        if session_id in self.active_sessions:
            del self.active_sessions[session_id]
    
    def generate_api_key(self, user_id: int, description: str = "") -> tuple[str, str]:
        """Générer une clé API"""
        # Générer une clé aléatoire
        key = f"c2ew_{secrets.token_urlsafe(32)}"
        
        # Créer un hash pour stockage sécurisé
        key_hash = hashlib.sha256(f"{key}{settings.SECRET_KEY}".encode()).hexdigest()
        
        # Stocker les métadonnées
        self.api_keys[key_hash] = {
            'user_id': user_id,
            'description': description,
            'created_at': datetime.utcnow(),
            'last_used': None,
            'usage_count': 0
        }
        
        return key, key_hash
    
    def verify_api_key(self, api_key: str) -> Optional[dict]:
        """Vérifier une clé API"""
        key_hash = hashlib.sha256(f"{api_key}{settings.SECRET_KEY}".encode()).hexdigest()
        
        if key_hash in self.api_keys:
            key_info = self.api_keys[key_hash]
            key_info['last_used'] = datetime.utcnow()
            key_info['usage_count'] += 1
            return key_info
        
        return None
    
    def revoke_api_key(self, key_hash: str):
        """Révoquer une clé API"""
        if key_hash in self.api_keys:
            del self.api_keys[key_hash]
            logger.info("Clé API révoquée", key_hash=key_hash[:8] + "...")
    
    def generate_csrf_token(self, session_id: str) -> str:
        """Générer un token CSRF"""
        data = f"{session_id}{datetime.utcnow().isoformat()}{settings.SECRET_KEY}"
        return hashlib.sha256(data.encode()).hexdigest()
    
    def sanitize_input(self, input_string: str) -> str:
        """Nettoyer les entrées utilisateur"""
        if not isinstance(input_string, str):
            return ""
        
        # Supprimer les caractères dangereux
        dangerous_chars = ['<', '>', '"', "'", '&', '\x00', '\r', '\n']
        for char in dangerous_chars:
            input_string = input_string.replace(char, '')
        
        # Limiter la longueur
        return input_string[:1000]
    
    def audit_log(self, user_id: int, action: str, details: Dict[str, Any], ip_address: str):
        """Enregistrer une action dans les logs d'audit"""
        logger.info("Action utilisateur", 
                   user_id=user_id,
                   action=action,
                   details=details,
                   ip_address=ip_address,
                   timestamp=datetime.utcnow().isoformat())
    
    def validate_equipment_access(self, user_id: int, equipment_id: int, action: str) -> bool:
        """Valider l'accès à un équipement"""
        # Ici vous implémenteriez la logique de contrôle d'accès
        # basée sur les rôles, groupes, etc.
        return True  # Placeholder
    
    def encrypt_sensitive_data(self, data: str) -> str:
        """Chiffrer des données sensibles"""
        # Implémentation simplifiée - utilisez une vraie bibliothèque de chiffrement
        import base64
        encoded = base64.b64encode(data.encode()).decode()
        return encoded
    
    def decrypt_sensitive_data(self, encrypted_data: str) -> str:
        """Déchiffrer des données sensibles"""
        # Implémentation simplifiée
        import base64
        try:
            decoded = base64.b64decode(encrypted_data.encode()).decode()
            return decoded
        except Exception:
            return ""
    
    def get_security_headers(self) -> Dict[str, str]:
        """Obtenir les en-têtes de sécurité HTTP"""
        return {
            "X-Content-Type-Options": "nosniff",
            "X-Frame-Options": "DENY",
            "X-XSS-Protection": "1; mode=block",
            "Strict-Transport-Security": "max-age=31536000; includeSubDomains",
            "Content-Security-Policy": "default-src 'self'; script-src 'self' 'unsafe-inline'; style-src 'self' 'unsafe-inline'",
            "Referrer-Policy": "strict-origin-when-cross-origin"
        }
    
    def validate_file_upload(self, filename: str, content: bytes, max_size: int = 10 * 1024 * 1024) -> bool:
        """Valider un upload de fichier"""
        # Vérifier la taille
        if len(content) > max_size:
            return False
        
        # Vérifier l'extension
        allowed_extensions = ['.json', '.xml', '.csv', '.txt', '.zip']
        if not any(filename.lower().endswith(ext) for ext in allowed_extensions):
            return False
        
        # Vérifier les signatures de fichier (magic numbers)
        dangerous_signatures = [
            b'\x4D\x5A',  # PE executable
            b'\x7F\x45\x4C\x46',  # ELF executable
            b'\xCA\xFE\xBA\xBE',  # Java class file
        ]
        
        for sig in dangerous_signatures:
            if content.startswith(sig):
                return False
        
        return True
    
    def get_user_permissions(self, user_role: str) -> list:
        """Obtenir les permissions d'un utilisateur selon son rôle"""
        role_permissions = {
            'admin': ['*'],
            'operator': [
                'equipment:read', 'equipment:write', 'equipment:control',
                'plugin:read', 'plugin:configure',
                'user:read', 'system:logs', 'system:monitor',
                'geo:read', 'geo:write', 'audio:stream', 'audio:record'
            ],
            'technician': [
                'equipment:read', 'equipment:write',
                'plugin:read', 'plugin:configure',
                'user:read', 'system:logs', 'system:monitor',
                'geo:read', 'audio:stream'
            ],
            'viewer': [
                'equipment:read', 'plugin:read', 'user:read',
                'system:monitor', 'geo:read'
            ]
        }
        return role_permissions.get(user_role, [])
    
    def has_permission(self, user_role: str, required_permission: str) -> bool:
        """Vérifier si un utilisateur a une permission"""
        permissions = self.get_user_permissions(user_role)
        return '*' in permissions or required_permission in permissions

# Instance globale du gestionnaire de sécurité
security_manager = SecurityManager()
