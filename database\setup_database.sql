-- Script de configuration de la base de données C2-EW
-- PostgreSQL avec extension PostGIS

-- Créer la base de données (à exécuter en tant que superuser)
-- CREATE DATABASE c2ew_db WITH ENCODING 'UTF8' LC_COLLATE='fr_FR.UTF-8' LC_CTYPE='fr_FR.UTF-8';

-- Se connecter à la base de données c2ew_db avant d'exécuter le reste

-- Activer les extensions nécessaires
CREATE EXTENSION IF NOT EXISTS postgis;
CREATE EXTENSION IF NOT EXISTS postgis_topology;
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS pgcrypto;

-- <PERSON><PERSON><PERSON> le schéma principal
CREATE SCHEMA IF NOT EXISTS c2ew;

-- Dé<PERSON>ir le schéma par défaut
SET search_path TO c2ew, public;

-- Table des utilisateurs
CREATE TABLE IF NOT EXISTS users (
    id SERIAL PRIMARY KEY,
    username VARCHAR(50) UNIQUE NOT NULL,
    email VARCHAR(100) UNIQUE NOT NULL,
    full_name VARCHAR(100) NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    role VARCHAR(20) NOT NULL CHECK (role IN ('admin', 'operator', 'technician', 'viewer')),
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    last_login TIMESTAMP WITH TIME ZONE
);

-- Table des équipements
CREATE TABLE IF NOT EXISTS equipment (
    id SERIAL PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    type VARCHAR(50) NOT NULL CHECK (type IN ('comint', 'elint', 'anti_drone', 'jammer', 'sensor')),
    status VARCHAR(20) NOT NULL DEFAULT 'offline' CHECK (status IN ('active', 'standby', 'offline', 'maintenance', 'error')),
    description TEXT,
    location GEOMETRY(POINT, 4326), -- Coordonnées géographiques WGS84
    altitude REAL, -- Altitude en mètres
    configuration JSONB, -- Configuration spécifique à l'équipement
    metrics JSONB, -- Métriques temps réel
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    created_by INTEGER REFERENCES users(id)
);

-- Table des plugins
CREATE TABLE IF NOT EXISTS plugins (
    id VARCHAR(50) PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    display_name VARCHAR(100) NOT NULL,
    version VARCHAR(20) NOT NULL,
    description TEXT,
    author VARCHAR(100),
    status VARCHAR(20) NOT NULL DEFAULT 'inactive' CHECK (status IN ('active', 'inactive', 'error')),
    supported_equipment_types TEXT[], -- Array des types d'équipements supportés
    configuration JSONB, -- Configuration du plugin
    manifest JSONB, -- Manifeste complet du plugin
    installed_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Table des commandes
CREATE TABLE IF NOT EXISTS commands (
    id SERIAL PRIMARY KEY,
    equipment_id INTEGER NOT NULL REFERENCES equipment(id) ON DELETE CASCADE,
    plugin_id VARCHAR(50) REFERENCES plugins(id),
    command_name VARCHAR(100) NOT NULL,
    parameters JSONB,
    status VARCHAR(20) NOT NULL DEFAULT 'pending' CHECK (status IN ('pending', 'executing', 'completed', 'failed', 'cancelled')),
    result JSONB,
    error_message TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    executed_at TIMESTAMP WITH TIME ZONE,
    completed_at TIMESTAMP WITH TIME ZONE,
    created_by INTEGER NOT NULL REFERENCES users(id)
);

-- Table des alertes
CREATE TABLE IF NOT EXISTS alerts (
    id SERIAL PRIMARY KEY,
    title VARCHAR(200) NOT NULL,
    message TEXT NOT NULL,
    level VARCHAR(20) NOT NULL CHECK (level IN ('info', 'warning', 'error', 'critical')),
    status VARCHAR(20) NOT NULL DEFAULT 'unread' CHECK (status IN ('unread', 'read', 'resolved', 'dismissed')),
    equipment_id INTEGER REFERENCES equipment(id),
    location GEOMETRY(POINT, 4326),
    metadata JSONB,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    resolved_at TIMESTAMP WITH TIME ZONE,
    resolved_by INTEGER REFERENCES users(id)
);

-- Table des sessions utilisateur
CREATE TABLE IF NOT EXISTS user_sessions (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id INTEGER NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    token_hash VARCHAR(255) NOT NULL,
    ip_address INET,
    user_agent TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    expires_at TIMESTAMP WITH TIME ZONE NOT NULL,
    last_activity TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Table des logs d'audit
CREATE TABLE IF NOT EXISTS audit_logs (
    id SERIAL PRIMARY KEY,
    user_id INTEGER REFERENCES users(id),
    action VARCHAR(100) NOT NULL,
    resource_type VARCHAR(50),
    resource_id INTEGER,
    details JSONB,
    ip_address INET,
    user_agent TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Table des zones géographiques
CREATE TABLE IF NOT EXISTS geographic_zones (
    id SERIAL PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    description TEXT,
    zone_type VARCHAR(50) NOT NULL CHECK (zone_type IN ('operational', 'restricted', 'monitoring', 'exclusion')),
    geometry GEOMETRY(POLYGON, 4326), -- Zone polygonale
    properties JSONB,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    created_by INTEGER NOT NULL REFERENCES users(id)
);

-- Index géospatiaux
CREATE INDEX IF NOT EXISTS idx_equipment_location ON equipment USING GIST (location);
CREATE INDEX IF NOT EXISTS idx_alerts_location ON alerts USING GIST (location);
CREATE INDEX IF NOT EXISTS idx_geographic_zones_geometry ON geographic_zones USING GIST (geometry);

-- Index pour les performances
CREATE INDEX IF NOT EXISTS idx_equipment_type ON equipment(type);
CREATE INDEX IF NOT EXISTS idx_equipment_status ON equipment(status);
CREATE INDEX IF NOT EXISTS idx_alerts_level ON alerts(level);
CREATE INDEX IF NOT EXISTS idx_alerts_status ON alerts(status);
CREATE INDEX IF NOT EXISTS idx_commands_status ON commands(status);
CREATE INDEX IF NOT EXISTS idx_commands_equipment_id ON commands(equipment_id);
CREATE INDEX IF NOT EXISTS idx_user_sessions_user_id ON user_sessions(user_id);
CREATE INDEX IF NOT EXISTS idx_user_sessions_expires_at ON user_sessions(expires_at);

-- Fonction pour mettre à jour updated_at automatiquement
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Triggers pour updated_at
CREATE TRIGGER update_users_updated_at BEFORE UPDATE ON users FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_equipment_updated_at BEFORE UPDATE ON equipment FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_plugins_updated_at BEFORE UPDATE ON plugins FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_alerts_updated_at BEFORE UPDATE ON alerts FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Insérer les utilisateurs par défaut
INSERT INTO users (username, email, full_name, password_hash, role) VALUES
('admin', '<EMAIL>', 'Administrateur', crypt('admin123', gen_salt('bf')), 'admin'),
('operator', '<EMAIL>', 'Opérateur', crypt('op123', gen_salt('bf')), 'operator'),
('technician', '<EMAIL>', 'Technicien', crypt('tech123', gen_salt('bf')), 'technician'),
('viewer', '<EMAIL>', 'Observateur', crypt('view123', gen_salt('bf')), 'viewer')
ON CONFLICT (username) DO NOTHING;

-- Insérer des équipements de démonstration
INSERT INTO equipment (name, type, status, description, location, configuration, metrics, created_by) VALUES
(
    'ICOM IC-R8600',
    'comint',
    'active',
    'Récepteur de communication large bande',
    ST_SetSRID(ST_MakePoint(2.3522, 48.8566), 4326), -- Paris
    '{"frequency_range": "0.01-3000 MHz", "modes": ["AM", "FM", "USB", "LSB", "CW"], "ip_address": "*************", "port": 50001}',
    '{"frequency": "145.500 MHz", "signal_strength": -65, "mode": "FM", "bandwidth": "12.5 kHz"}',
    1
),
(
    'Anti-Drone System Alpha',
    'anti_drone',
    'standby',
    'Système de détection et neutralisation de drones',
    ST_SetSRID(ST_MakePoint(2.3376, 48.8606), 4326), -- Louvre
    '{"detection_range": 2000, "jamming_power": 50, "frequencies": ["2.4 GHz", "5.8 GHz"]}',
    '{"targets_tracked": 0, "jamming_active": false, "power_level": 50}',
    1
),
(
    'ELINT Sensor Bravo',
    'elint',
    'active',
    'Capteur de renseignement électronique',
    ST_SetSRID(ST_MakePoint(2.3488, 48.8534), 4326), -- Notre-Dame
    '{"frequency_range": "1-18 GHz", "analysis_modes": ["auto", "manual"], "sensitivity": -120}',
    '{"signals_detected": 15, "analysis_mode": "auto", "cpu_usage": 45}',
    1
)
ON CONFLICT DO NOTHING;

-- Insérer des plugins de démonstration
INSERT INTO plugins (id, name, display_name, version, description, author, status, supported_equipment_types, manifest) VALUES
(
    'icom-r8600',
    'icom-r8600',
    'ICOM IC-R8600',
    '1.0.0',
    'Plugin pour récepteur ICOM IC-R8600',
    'C2-EW Team',
    'active',
    ARRAY['comint'],
    '{"commands": [{"name": "set_frequency", "description": "Changer la fréquence"}, {"name": "set_mode", "description": "Changer le mode"}], "ui_components": {"main": "ICOMInterface.jsx"}}'
)
ON CONFLICT (id) DO NOTHING;

-- Insérer des alertes de démonstration
INSERT INTO alerts (title, message, level, equipment_id, location, metadata) VALUES
(
    'Signal suspect détecté',
    'Fréquence 446.000 MHz - Signal non identifié détecté par ICOM IC-R8600',
    'warning',
    1,
    ST_SetSRID(ST_MakePoint(2.3522, 48.8566), 4326),
    '{"frequency": "446.000 MHz", "signal_strength": -45, "duration": 120}'
),
(
    'Drone détecté',
    'Drone hostile détecté dans la zone de surveillance',
    'critical',
    2,
    ST_SetSRID(ST_MakePoint(2.3376, 48.8606), 4326),
    '{"drone_type": "DJI Phantom", "altitude": 50, "speed": 15}'
);

-- Créer une vue pour les équipements avec leurs coordonnées
CREATE OR REPLACE VIEW equipment_with_coordinates AS
SELECT 
    e.*,
    ST_X(e.location) as longitude,
    ST_Y(e.location) as latitude,
    ST_Z(e.location) as altitude_from_geom
FROM equipment e
WHERE e.location IS NOT NULL;

-- Fonction pour calculer la distance entre deux équipements
CREATE OR REPLACE FUNCTION equipment_distance(equipment_id1 INTEGER, equipment_id2 INTEGER)
RETURNS REAL AS $$
DECLARE
    distance REAL;
BEGIN
    SELECT ST_Distance(
        ST_Transform(e1.location, 3857),
        ST_Transform(e2.location, 3857)
    ) INTO distance
    FROM equipment e1, equipment e2
    WHERE e1.id = equipment_id1 AND e2.id = equipment_id2;
    
    RETURN distance;
END;
$$ LANGUAGE plpgsql;

-- Afficher un résumé de la configuration
SELECT 'Base de données C2-EW configurée avec succès!' as message;
SELECT 'Utilisateurs créés: ' || count(*) as users_count FROM users;
SELECT 'Équipements créés: ' || count(*) as equipment_count FROM equipment;
SELECT 'Plugins installés: ' || count(*) as plugins_count FROM plugins;
SELECT 'Extensions PostGIS activées' as postgis_status;
