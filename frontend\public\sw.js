// Service Worker pour la plateforme C2-EW
// Gestion du cache et fonctionnalités offline

const CACHE_NAME = 'c2ew-v1.0.0';
const STATIC_CACHE = 'c2ew-static-v1.0.0';
const DYNAMIC_CACHE = 'c2ew-dynamic-v1.0.0';

// Ressources à mettre en cache immédiatement
const STATIC_ASSETS = [
  '/',
  '/index.html',
  '/manifest.json',
  '/favicon.ico',
  // Les assets seront ajoutés automatiquement par Vite
];

// Ressources critiques qui doivent toujours être fraîches
const CRITICAL_RESOURCES = [
  '/api/v1/auth/me',
  '/api/v1/equipment',
  '/api/v1/alerts',
];

// Installation du Service Worker
self.addEventListener('install', (event) => {
  console.log('[SW] Installation');
  
  event.waitUntil(
    caches.open(STATIC_CACHE)
      .then((cache) => {
        console.log('[SW] Mise en cache des ressources statiques');
        return cache.addAll(STATIC_ASSETS);
      })
      .then(() => {
        console.log('[SW] Installation terminée');
        return self.skipWaiting();
      })
      .catch((error) => {
        console.error('[SW] Erreur lors de l\'installation:', error);
      })
  );
});

// Activation du Service Worker
self.addEventListener('activate', (event) => {
  console.log('[SW] Activation');
  
  event.waitUntil(
    caches.keys()
      .then((cacheNames) => {
        return Promise.all(
          cacheNames.map((cacheName) => {
            if (cacheName !== STATIC_CACHE && cacheName !== DYNAMIC_CACHE) {
              console.log('[SW] Suppression de l\'ancien cache:', cacheName);
              return caches.delete(cacheName);
            }
          })
        );
      })
      .then(() => {
        console.log('[SW] Activation terminée');
        return self.clients.claim();
      })
  );
});

// Interception des requêtes
self.addEventListener('fetch', (event) => {
  const { request } = event;
  const url = new URL(request.url);
  
  // Ignorer les requêtes non-HTTP
  if (!request.url.startsWith('http')) {
    return;
  }
  
  // Stratégie pour les ressources statiques
  if (isStaticAsset(request)) {
    event.respondWith(cacheFirst(request));
    return;
  }
  
  // Stratégie pour les API critiques
  if (isCriticalAPI(request)) {
    event.respondWith(networkFirst(request));
    return;
  }
  
  // Stratégie pour les autres API
  if (isAPI(request)) {
    event.respondWith(networkOnly(request));
    return;
  }
  
  // Stratégie par défaut pour les autres ressources
  event.respondWith(staleWhileRevalidate(request));
});

// Stratégie Cache First (pour les assets statiques)
async function cacheFirst(request) {
  try {
    const cachedResponse = await caches.match(request);
    if (cachedResponse) {
      return cachedResponse;
    }
    
    const networkResponse = await fetch(request);
    if (networkResponse.ok) {
      const cache = await caches.open(STATIC_CACHE);
      cache.put(request, networkResponse.clone());
    }
    
    return networkResponse;
  } catch (error) {
    console.error('[SW] Erreur cache first:', error);
    return new Response('Ressource non disponible', { status: 503 });
  }
}

// Stratégie Network First (pour les API critiques)
async function networkFirst(request) {
  try {
    const networkResponse = await fetch(request);
    if (networkResponse.ok) {
      const cache = await caches.open(DYNAMIC_CACHE);
      cache.put(request, networkResponse.clone());
    }
    return networkResponse;
  } catch (error) {
    console.warn('[SW] Réseau indisponible, utilisation du cache:', request.url);
    const cachedResponse = await caches.match(request);
    if (cachedResponse) {
      return cachedResponse;
    }
    
    return new Response(JSON.stringify({
      error: 'Service temporairement indisponible',
      offline: true
    }), {
      status: 503,
      headers: { 'Content-Type': 'application/json' }
    });
  }
}

// Stratégie Network Only (pour les API temps réel)
async function networkOnly(request) {
  try {
    return await fetch(request);
  } catch (error) {
    return new Response(JSON.stringify({
      error: 'Connexion réseau requise',
      offline: true
    }), {
      status: 503,
      headers: { 'Content-Type': 'application/json' }
    });
  }
}

// Stratégie Stale While Revalidate
async function staleWhileRevalidate(request) {
  const cache = await caches.open(DYNAMIC_CACHE);
  const cachedResponse = await cache.match(request);
  
  const fetchPromise = fetch(request).then((networkResponse) => {
    if (networkResponse.ok) {
      cache.put(request, networkResponse.clone());
    }
    return networkResponse;
  }).catch(() => cachedResponse);
  
  return cachedResponse || fetchPromise;
}

// Utilitaires de classification des requêtes
function isStaticAsset(request) {
  const url = new URL(request.url);
  return url.pathname.match(/\.(js|css|png|jpg|jpeg|gif|svg|ico|woff|woff2|ttf|eot)$/);
}

function isCriticalAPI(request) {
  return CRITICAL_RESOURCES.some(resource => request.url.includes(resource));
}

function isAPI(request) {
  return request.url.includes('/api/');
}

// Gestion des messages du client
self.addEventListener('message', (event) => {
  const { type, payload } = event.data;
  
  switch (type) {
    case 'SKIP_WAITING':
      self.skipWaiting();
      break;
      
    case 'GET_CACHE_SIZE':
      getCacheSize().then((size) => {
        event.ports[0].postMessage({ type: 'CACHE_SIZE', size });
      });
      break;
      
    case 'CLEAR_CACHE':
      clearCache().then(() => {
        event.ports[0].postMessage({ type: 'CACHE_CLEARED' });
      });
      break;
      
    case 'FORCE_UPDATE':
      forceUpdate();
      break;
  }
});

// Obtenir la taille du cache
async function getCacheSize() {
  const cacheNames = await caches.keys();
  let totalSize = 0;
  
  for (const cacheName of cacheNames) {
    const cache = await caches.open(cacheName);
    const requests = await cache.keys();
    
    for (const request of requests) {
      const response = await cache.match(request);
      if (response) {
        const blob = await response.blob();
        totalSize += blob.size;
      }
    }
  }
  
  return totalSize;
}

// Vider le cache
async function clearCache() {
  const cacheNames = await caches.keys();
  await Promise.all(
    cacheNames.map(cacheName => caches.delete(cacheName))
  );
}

// Forcer la mise à jour
function forceUpdate() {
  self.registration.update();
}

// Notification de mise à jour disponible
self.addEventListener('updatefound', () => {
  const newWorker = self.registration.installing;
  
  newWorker.addEventListener('statechange', () => {
    if (newWorker.state === 'installed' && navigator.serviceWorker.controller) {
      // Nouvelle version disponible
      self.clients.matchAll().then((clients) => {
        clients.forEach((client) => {
          client.postMessage({
            type: 'UPDATE_AVAILABLE',
            message: 'Une nouvelle version est disponible'
          });
        });
      });
    }
  });
});

console.log('[SW] Service Worker C2-EW chargé');

// ========================================
// EXTENSION POUR CARTE OFFLINE MAROC
// ========================================

const TILES_CACHE = 'morocco-tiles-v1.0.0';

// Configuration des serveurs de tiles NASA
const NASA_TILE_SERVERS = [
  'https://map1.vis.earthdata.nasa.gov',
  'https://gibs.earthdata.nasa.gov',
  'https://files.worldwind.arc.nasa.gov'
];

// Ajouter la gestion des tiles à l'interception des requêtes
const originalFetchHandler = self.addEventListener;

// Remplacer le gestionnaire fetch existant
self.addEventListener('fetch', (event) => {
  const { request } = event;
  const url = new URL(request.url);

  // Ignorer les requêtes non-HTTP
  if (!request.url.startsWith('http')) {
    return;
  }

  // NOUVEAU: Intercepter les requêtes de tiles NASA
  if (isNASATileRequest(url)) {
    event.respondWith(handleTileRequest(request));
    return;
  }

  // NOUVEAU: Intercepter les requêtes WebWorldWind
  if (isWebWorldWindRequest(url)) {
    event.respondWith(handleWebWorldWindRequest(request));
    return;
  }

  // Stratégie pour les ressources statiques
  if (isStaticAsset(request)) {
    event.respondWith(cacheFirst(request));
    return;
  }

  // Stratégie pour les API critiques
  if (isCriticalAPI(request)) {
    event.respondWith(networkFirst(request));
    return;
  }

  // Stratégie pour les autres API
  if (isAPI(request)) {
    event.respondWith(networkOnly(request));
    return;
  }

  // Stratégie par défaut pour les autres ressources
  event.respondWith(staleWhileRevalidate(request));
});

// Vérifier si c'est une requête de tile NASA
function isNASATileRequest(url) {
  return NASA_TILE_SERVERS.some(server => url.href.includes(server)) ||
         url.pathname.includes('/wmts/') ||
         url.pathname.includes('/tiles/') ||
         (url.pathname.match(/\/\d+\/\d+\/\d+\.(jpg|png|jpeg)$/));
}

// Vérifier si c'est une requête WebWorldWind
function isWebWorldWindRequest(url) {
  return url.href.includes('worldwind.arc.nasa.gov') ||
         url.href.includes('files.worldwind.arc.nasa.gov');
}

// Gérer les requêtes de tiles
async function handleTileRequest(request) {
  const url = new URL(request.url);

  try {
    console.log('[SW] Requête tile:', url.pathname);

    // 1. Chercher d'abord dans le cache des tiles
    const tilesCache = await caches.open(TILES_CACHE);
    const cachedResponse = await tilesCache.match(request);
    if (cachedResponse) {
      console.log('[SW] 📍 Tile servie depuis le cache:', url.pathname);
      return cachedResponse;
    }

    // 2. Chercher dans les tiles locales
    const localTile = await getLocalTile(url);
    if (localTile) {
      console.log('[SW] 🗺️ Tile servie depuis le stockage local:', url.pathname);
      return localTile;
    }

    // 3. Si online, télécharger et mettre en cache
    if (navigator.onLine) {
      console.log('[SW] 📥 Téléchargement tile:', url.pathname);
      const response = await fetch(request);

      if (response.ok) {
        // Mettre en cache pour usage futur
        tilesCache.put(request, response.clone());
        console.log('[SW] ✅ Tile mise en cache:', url.pathname);
        return response;
      }
    }

    // 4. Fallback - tile par défaut
    console.log('[SW] 🎨 Génération tile par défaut pour:', url.pathname);
    return createDefaultTile();

  } catch (error) {
    console.warn('[SW] ❌ Erreur tile:', error);
    return createDefaultTile();
  }
}

// Gérer les requêtes WebWorldWind
async function handleWebWorldWindRequest(request) {
  try {
    // Chercher d'abord en cache
    const cachedResponse = await caches.match(request);
    if (cachedResponse) {
      console.log('[SW] WebWorldWind servi depuis le cache');
      return cachedResponse;
    }

    // Si online, télécharger
    if (navigator.onLine) {
      const response = await fetch(request);
      if (response.ok) {
        const cache = await caches.open(STATIC_CACHE);
        cache.put(request, response.clone());
        return response;
      }
    }

    // Fallback vers version locale si disponible
    const localPath = request.url.replace(/.*\/([^\/]+)$/, '/$1');
    try {
      return await fetch(localPath);
    } catch {
      return new Response('', { status: 404 });
    }

  } catch (error) {
    console.warn('[SW] ❌ Erreur WebWorldWind:', error);
    return new Response('', { status: 404 });
  }
}

// Récupérer une tile locale
async function getLocalTile(url) {
  try {
    // Convertir l'URL NASA en chemin local
    const localPath = convertToLocalPath(url);
    const localUrl = new URL(localPath, self.location.origin);

    const response = await fetch(localUrl);
    if (response.ok) {
      return response;
    }
  } catch (error) {
    // Tile locale non trouvée
  }
  return null;
}

// Convertir URL NASA en chemin local
function convertToLocalPath(url) {
  // Extraire les coordonnées de tile z/x/y
  const match = url.pathname.match(/(\d+)\/(\d+)\/(\d+)\.(jpg|png|jpeg)$/);
  if (match) {
    const [, z, x, y, ext] = match;
    return `/tiles/morocco/${z}/${x}/${y}.${ext}`;
  }

  // Patterns spécifiques NASA
  if (url.pathname.includes('MODIS') || url.pathname.includes('VIIRS')) {
    const match2 = url.pathname.match(/Level(\d+)\/(\d+)\/(\d+)\/(\d+)\.(jpg|png)$/);
    if (match2) {
      const [, level, z, x, y, ext] = match2;
      return `/tiles/morocco/${z}/${x}/${y}.${ext}`;
    }
  }

  // Fallback pour autres formats
  return `/tiles/morocco/default.png`;
}

// Créer une tile par défaut
async function createDefaultTile() {
  try {
    // Créer une image 256x256 simple
    const canvas = new OffscreenCanvas(256, 256);
    const ctx = canvas.getContext('2d');

    // Fond beige (couleur terre du Maroc)
    ctx.fillStyle = '#D2B48C';
    ctx.fillRect(0, 0, 256, 256);

    // Bordure
    ctx.strokeStyle = '#8B4513';
    ctx.lineWidth = 2;
    ctx.strokeRect(1, 1, 254, 254);

    // Texte "OFFLINE"
    ctx.fillStyle = '#8B4513';
    ctx.font = 'bold 16px Arial';
    ctx.textAlign = 'center';
    ctx.fillText('OFFLINE', 128, 120);
    ctx.fillText('MAROC', 128, 140);

    // Petite icône
    ctx.fillStyle = '#CD853F';
    ctx.fillRect(110, 160, 36, 20);
    ctx.fillStyle = '#8B4513';
    ctx.fillRect(115, 165, 26, 10);

    const blob = await canvas.convertToBlob({ type: 'image/png' });
    return new Response(blob, {
      headers: {
        'Content-Type': 'image/png',
        'Cache-Control': 'public, max-age=31536000'
      }
    });
  } catch (error) {
    // Fallback simple si OffscreenCanvas n'est pas supporté
    return new Response('', {
      status: 200,
      headers: { 'Content-Type': 'image/png' }
    });
  }
}

// Extension des messages pour les tiles
const originalMessageHandler = self.addEventListener;

// Ajouter de nouveaux types de messages
self.addEventListener('message', (event) => {
  const { type, payload } = event.data;

  switch (type) {
    case 'SKIP_WAITING':
      self.skipWaiting();
      break;

    case 'GET_CACHE_SIZE':
      getCacheSize().then((size) => {
        event.ports[0].postMessage({ type: 'CACHE_SIZE', size });
      });
      break;

    case 'CLEAR_CACHE':
      clearCache().then(() => {
        event.ports[0].postMessage({ type: 'CACHE_CLEARED' });
      });
      break;

    case 'FORCE_UPDATE':
      forceUpdate();
      break;

    // NOUVEAUX: Messages pour les tiles
    case 'CACHE_TILES':
      console.log('[SW] 📥 Demande de mise en cache des tiles');
      cacheTilesForMorocco(payload.tiles).then((result) => {
        event.ports[0].postMessage({
          type: 'TILES_CACHED',
          cached: result.cached,
          total: result.total
        });
      });
      break;

    case 'GET_TILES_STATUS':
      getTilesStatus().then((status) => {
        event.ports[0].postMessage({
          type: 'TILES_STATUS',
          status
        });
      });
      break;

    case 'CLEAR_TILES_CACHE':
      clearTilesCache().then(() => {
        event.ports[0].postMessage({ type: 'TILES_CACHE_CLEARED' });
      });
      break;
  }
});

// Mettre en cache les tiles pour le Maroc
async function cacheTilesForMorocco(tiles) {
  const cache = await caches.open(TILES_CACHE);
  let cached = 0;

  console.log(`[SW] Début mise en cache de ${tiles.length} tiles`);

  for (const tile of tiles) {
    try {
      const response = await fetch(tile.url);
      if (response.ok) {
        await cache.put(tile.url, response);
        cached++;

        if (cached % 10 === 0) {
          console.log(`[SW] 📥 ${cached}/${tiles.length} tiles mises en cache`);
        }
      }
    } catch (error) {
      console.warn('[SW] ❌ Erreur cache tile:', tile.url);
    }
  }

  console.log(`[SW] ✅ ${cached}/${tiles.length} tiles mises en cache avec succès`);
  return { cached, total: tiles.length };
}

// Obtenir le statut des tiles
async function getTilesStatus() {
  const cache = await caches.open(TILES_CACHE);
  const keys = await cache.keys();

  let totalSize = 0;
  for (const key of keys.slice(0, 10)) { // Échantillon pour estimation
    try {
      const response = await cache.match(key);
      if (response) {
        const blob = await response.blob();
        totalSize += blob.size;
      }
    } catch (error) {
      // Ignorer les erreurs
    }
  }

  // Estimation basée sur l'échantillon
  const estimatedSize = Math.round((totalSize * keys.length) / Math.min(10, keys.length));

  return {
    tilesCount: keys.length,
    cacheSize: estimatedSize,
    lastUpdate: new Date().toISOString(),
    isReady: keys.length > 50 // Considéré prêt avec 50+ tiles
  };
}

// Vider le cache des tiles
async function clearTilesCache() {
  await caches.delete(TILES_CACHE);
  console.log('[SW] 🗑️ Cache des tiles vidé');
}
