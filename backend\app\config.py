from pydantic_settings import BaseSettings
from typing import List, Optional
import os


class Settings(BaseSettings):
    # Base de données
    DATABASE_URL: str = "postgresql://c2ew_user:c2ew_password@localhost:5432/c2ew_db"
    
    # Sécurité
    SECRET_KEY: str = "your-super-secret-key-change-this-in-production"
    ALGORITHM: str = "HS256"
    ACCESS_TOKEN_EXPIRE_MINUTES: int = 30
    
    # API Configuration
    API_V1_STR: str = "/api/v1"
    PROJECT_NAME: str = "C2-EW Platform"
    VERSION: str = "1.0.0"
    DESCRIPTION: str = "Command & Control Electronic Warfare Platform"
    
    # CORS
    BACKEND_CORS_ORIGINS: List[str] = [
        "http://localhost:3000",
        "https://localhost:3000",
        "http://localhost",
        "https://localhost"
    ]
    
    # Environment
    ENVIRONMENT: str = "development"
    DEBUG: bool = True
    
    # Logging
    LOG_LEVEL: str = "INFO"
    LOG_FORMAT: str = "json"
    
    # WebSocket
    WS_HEARTBEAT_INTERVAL: int = 30
    WS_MAX_CONNECTIONS: int = 100
    
    # Plugins
    PLUGINS_DIR: str = "./plugins"
    PLUGINS_ENABLED: bool = True
    
    # Géospatial
    DEFAULT_SRID: int = 4326
    MAP_TILES_URL: str = "http://localhost:8080/tiles/{z}/{x}/{y}.pbf"
    
    # Audio streaming
    AUDIO_SAMPLE_RATE: int = 48000
    AUDIO_CHANNELS: int = 2
    AUDIO_BUFFER_SIZE: int = 1024
    
    # Equipment communication
    SERIAL_TIMEOUT: int = 5
    USB_TIMEOUT: int = 10
    NETWORK_TIMEOUT: int = 30
    
    # Cache
    REDIS_URL: str = "redis://localhost:6379/0"
    CACHE_TTL: int = 300
    
    # Monitoring
    METRICS_ENABLED: bool = True
    HEALTH_CHECK_INTERVAL: int = 60

    class Config:
        env_file = ".env"
        case_sensitive = True


settings = Settings()
