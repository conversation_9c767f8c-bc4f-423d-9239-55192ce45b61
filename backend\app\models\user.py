from sqlmodel import SQLModel, Field, Relationship
from typing import Optional, List
from datetime import datetime
from enum import Enum
import uuid

class UserRole(str, Enum):
    ADMIN = "admin"
    OPERATOR = "operator"
    VIEWER = "viewer"
    TECHNICIAN = "technician"

class User(SQLModel, table=True):
    """Modèle utilisateur avec RBAC"""
    __tablename__ = "users"
    
    id: Optional[int] = Field(default=None, primary_key=True)
    uuid: str = Field(default_factory=lambda: str(uuid.uuid4()), unique=True, index=True)
    
    # Informations de base
    username: str = Field(unique=True, index=True, min_length=3, max_length=50)
    email: str = Field(unique=True, index=True)
    full_name: str = Field(max_length=100)
    
    # Authentification
    hashed_password: str
    is_active: bool = Field(default=True)
    is_superuser: bool = Field(default=False)
    
    # Rôle et permissions
    role: UserRole = Field(default=UserRole.VIEWER)
    
    # Métadonnées
    created_at: datetime = Field(default_factory=datetime.utcnow)
    updated_at: Optional[datetime] = Field(default=None)
    last_login: Optional[datetime] = Field(default=None)
    
    # Préférences utilisateur
    preferences: Optional[dict] = Field(default_factory=dict, sa_column_kwargs={"type_": "JSON"})
    
    # Relations
    command_history: List["CommandHistory"] = Relationship(back_populates="user")
    alerts_created: List["Alert"] = Relationship(back_populates="created_by")

class UserSession(SQLModel, table=True):
    """Sessions utilisateur actives"""
    __tablename__ = "user_sessions"
    
    id: Optional[int] = Field(default=None, primary_key=True)
    user_id: int = Field(foreign_key="users.id")
    session_token: str = Field(unique=True, index=True)
    
    # Informations de session
    ip_address: Optional[str] = Field(max_length=45)  # IPv6 compatible
    user_agent: Optional[str] = Field(max_length=500)
    
    # Timestamps
    created_at: datetime = Field(default_factory=datetime.utcnow)
    expires_at: datetime
    last_activity: datetime = Field(default_factory=datetime.utcnow)
    
    # Statut
    is_active: bool = Field(default=True)

class UserActivity(SQLModel, table=True):
    """Log des activités utilisateur"""
    __tablename__ = "user_activities"
    
    id: Optional[int] = Field(default=None, primary_key=True)
    user_id: int = Field(foreign_key="users.id")
    
    # Détails de l'activité
    action: str = Field(max_length=100)
    resource: Optional[str] = Field(max_length=100)
    resource_id: Optional[str] = Field(max_length=100)
    
    # Contexte
    ip_address: Optional[str] = Field(max_length=45)
    user_agent: Optional[str] = Field(max_length=500)
    
    # Données additionnelles
    metadata: Optional[dict] = Field(default_factory=dict, sa_column_kwargs={"type_": "JSON"})
    
    # Timestamp
    created_at: datetime = Field(default_factory=datetime.utcnow)
