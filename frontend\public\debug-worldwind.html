<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🔧 Debug WebWorldWind</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            background-color: #1a1a1a;
            color: white;
            font-family: 'Courier New', monospace;
        }
        
        .debug-panel {
            background: #2a2a2a;
            border: 2px solid #4CAF50;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 20px;
        }
        
        .map-container {
            width: 100%;
            height: 400px;
            background: #000;
            border: 2px solid #f44336;
            border-radius: 5px;
            position: relative;
        }
        
        #worldWindCanvas {
            width: 100%;
            height: 100%;
            display: block;
            background: #333;
        }
        
        .log {
            background: #000;
            color: #0f0;
            padding: 15px;
            border-radius: 5px;
            font-size: 12px;
            max-height: 200px;
            overflow-y: auto;
            white-space: pre-wrap;
        }
        
        .btn {
            background: #4CAF50;
            color: white;
            border: none;
            padding: 10px 20px;
            margin: 5px;
            border-radius: 5px;
            cursor: pointer;
        }
        
        .btn:hover {
            background: #45a049;
        }
        
        .status {
            display: inline-block;
            padding: 5px 10px;
            border-radius: 3px;
            margin: 5px;
            font-weight: bold;
        }
        
        .status-ok { background: #4CAF50; }
        .status-error { background: #f44336; }
        .status-warning { background: #ff9800; }
    </style>
</head>
<body>
    <h1>🔧 Diagnostic WebWorldWind</h1>
    
    <div class="debug-panel">
        <h3>📊 État du Système</h3>
        <div id="systemStatus">
            <div>Navigateur: <span id="browserInfo">-</span></div>
            <div>WebGL: <span id="webglStatus" class="status status-warning">Test en cours...</span></div>
            <div>Canvas: <span id="canvasStatus" class="status status-warning">Test en cours...</span></div>
            <div>WebWorldWind: <span id="wwStatus" class="status status-warning">Chargement...</span></div>
            <div>Internet: <span id="internetStatus" class="status status-warning">Test en cours...</span></div>
        </div>
        
        <div style="margin-top: 15px;">
            <button class="btn" onclick="runDiagnostics()">🔍 Lancer Diagnostics</button>
            <button class="btn" onclick="testWebGL()">🎮 Tester WebGL</button>
            <button class="btn" onclick="initMap()">🗺️ Initialiser Carte</button>
            <button class="btn" onclick="clearLog()">🗑️ Vider Log</button>
        </div>
    </div>
    
    <div class="debug-panel">
        <h3>🗺️ Zone de Test WebWorldWind</h3>
        <div class="map-container">
            <canvas id="worldWindCanvas" width="800" height="400">
                Votre navigateur ne supporte pas HTML5 Canvas.
            </canvas>
        </div>
        <div style="margin-top: 10px; font-size: 12px;">
            Si vous voyez une carte ici, WebWorldWind fonctionne !
        </div>
    </div>
    
    <div class="debug-panel">
        <h3>📝 Log de Debug</h3>
        <div id="debugLog" class="log">Initialisation du diagnostic...\n</div>
    </div>

    <!-- WebWorldWind depuis CDN pour test -->
    <script src="https://files.worldwind.arc.nasa.gov/artifactory/web/0.11.0/worldwind.min.js" 
            onload="onWorldWindLoaded()" 
            onerror="onWorldWindError()">
    </script>

    <script>
        let worldWindow = null;
        let logElement = null;

        function log(message) {
            const timestamp = new Date().toLocaleTimeString();
            const logMessage = `[${timestamp}] ${message}\n`;
            
            if (!logElement) {
                logElement = document.getElementById('debugLog');
            }
            
            logElement.textContent += logMessage;
            logElement.scrollTop = logElement.scrollHeight;
            console.log(message);
        }

        function updateStatus(elementId, status, text) {
            const element = document.getElementById(elementId);
            element.textContent = text;
            element.className = `status status-${status}`;
        }

        function clearLog() {
            document.getElementById('debugLog').textContent = '';
            log('Log vidé');
        }

        function runDiagnostics() {
            log('🔍 Début des diagnostics...');
            
            // Test navigateur
            const browserInfo = `${navigator.userAgent.split(' ').slice(-2).join(' ')}`;
            document.getElementById('browserInfo').textContent = browserInfo;
            log(`Navigateur: ${browserInfo}`);
            
            // Test Canvas
            testCanvas();
            
            // Test WebGL
            testWebGL();
            
            // Test Internet
            testInternet();
            
            // Test WebWorldWind
            if (typeof WorldWind !== 'undefined') {
                updateStatus('wwStatus', 'ok', 'Chargé ✅');
                log('✅ WebWorldWind est disponible');
            } else {
                updateStatus('wwStatus', 'error', 'Non chargé ❌');
                log('❌ WebWorldWind non disponible');
            }
        }

        function testCanvas() {
            try {
                const canvas = document.getElementById('worldWindCanvas');
                const ctx = canvas.getContext('2d');
                
                if (ctx) {
                    // Dessiner un test simple
                    ctx.fillStyle = '#4CAF50';
                    ctx.fillRect(10, 10, 100, 50);
                    ctx.fillStyle = 'white';
                    ctx.font = '16px Arial';
                    ctx.fillText('Canvas OK', 15, 35);
                    
                    updateStatus('canvasStatus', 'ok', 'Fonctionnel ✅');
                    log('✅ Canvas 2D fonctionne');
                } else {
                    throw new Error('Impossible de créer le contexte 2D');
                }
            } catch (error) {
                updateStatus('canvasStatus', 'error', 'Erreur ❌');
                log(`❌ Erreur Canvas: ${error.message}`);
            }
        }

        function testWebGL() {
            try {
                const canvas = document.createElement('canvas');
                const gl = canvas.getContext('webgl') || canvas.getContext('experimental-webgl');
                
                if (gl) {
                    const renderer = gl.getParameter(gl.RENDERER);
                    const vendor = gl.getParameter(gl.VENDOR);
                    
                    updateStatus('webglStatus', 'ok', 'Supporté ✅');
                    log(`✅ WebGL supporté: ${renderer} (${vendor})`);
                    
                    // Test des extensions WebGL
                    const extensions = gl.getSupportedExtensions();
                    log(`Extensions WebGL: ${extensions.length} disponibles`);
                    
                } else {
                    throw new Error('WebGL non supporté');
                }
            } catch (error) {
                updateStatus('webglStatus', 'error', 'Non supporté ❌');
                log(`❌ Erreur WebGL: ${error.message}`);
                log('💡 Suggestion: Activez l\'accélération matérielle dans votre navigateur');
            }
        }

        async function testInternet() {
            try {
                const response = await fetch('https://www.google.com/favicon.ico', { 
                    mode: 'no-cors',
                    cache: 'no-cache'
                });
                updateStatus('internetStatus', 'ok', 'Connecté ✅');
                log('✅ Connexion internet active');
            } catch (error) {
                updateStatus('internetStatus', 'error', 'Déconnecté ❌');
                log('❌ Pas de connexion internet');
            }
        }

        function onWorldWindLoaded() {
            updateStatus('wwStatus', 'ok', 'Chargé ✅');
            log('✅ WebWorldWind chargé avec succès depuis le CDN');
            log(`Version WebWorldWind: ${WorldWind.VERSION || 'Inconnue'}`);
        }

        function onWorldWindError() {
            updateStatus('wwStatus', 'error', 'Erreur chargement ❌');
            log('❌ Échec du chargement de WebWorldWind depuis le CDN');
        }

        function initMap() {
            if (typeof WorldWind === 'undefined') {
                log('❌ WebWorldWind non disponible pour initialiser la carte');
                return;
            }

            try {
                log('🗺️ Initialisation de la carte...');
                
                const canvas = document.getElementById('worldWindCanvas');
                worldWindow = new WorldWind.WorldWindow(canvas);
                
                log('✅ WorldWindow créé');
                
                // Configuration basique
                worldWindow.navigator.lookAtLocation.latitude = 31.7917;
                worldWindow.navigator.lookAtLocation.longitude = -7.0926;
                worldWindow.navigator.range = 2000000;
                
                log('✅ Position configurée (Maroc)');
                
                // Ajouter une couche simple
                const bmngLayer = new WorldWind.BMNGOneImageLayer();
                worldWindow.addLayer(bmngLayer);
                
                log('✅ Couche d\'imagerie ajoutée');
                
                // Ajouter les contrôles
                const compassLayer = new WorldWind.CompassLayer();
                worldWindow.addLayer(compassLayer);
                
                const coordsLayer = new WorldWind.CoordinatesDisplayLayer(worldWindow);
                worldWindow.addLayer(coordsLayer);
                
                log('✅ Couches de contrôle ajoutées');
                
                // Forcer le rendu
                worldWindow.redraw();
                
                log('🎉 Carte initialisée avec succès !');
                log('👀 Vous devriez voir la carte du Maroc ci-dessus');
                
            } catch (error) {
                log(`❌ Erreur lors de l'initialisation: ${error.message}`);
                log(`Stack trace: ${error.stack}`);
            }
        }

        // Auto-diagnostic au chargement
        window.addEventListener('load', function() {
            log('🚀 Page chargée, lancement des diagnostics automatiques...');
            setTimeout(runDiagnostics, 1000);
            
            // Auto-init de la carte si WebWorldWind est disponible
            setTimeout(() => {
                if (typeof WorldWind !== 'undefined') {
                    initMap();
                }
            }, 3000);
        });

        // Gestion des erreurs globales
        window.addEventListener('error', function(event) {
            log(`❌ Erreur JavaScript: ${event.error.message}`);
        });
    </script>
</body>
</html>
