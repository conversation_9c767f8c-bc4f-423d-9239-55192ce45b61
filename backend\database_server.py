#!/usr/bin/env python3
"""
Serveur backend C2-EW avec base de données PostgreSQL/PostGIS
"""

import os
import json
import asyncio
from datetime import datetime, timedelta
from typing import List, Optional, Dict, Any
from contextlib import asynccontextmanager

from fastapi import FastAPI, HTTPException, Depends, WebSocket, WebSocketDisconnect, Query
from fastapi.middleware.cors import CORSMiddleware
from fastapi.security import HTT<PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from pydantic import BaseModel
from sqlalchemy import create_engine, text
from sqlalchemy.orm import sessionmaker
from dotenv import load_dotenv
import jwt
import uvicorn

# Charger les variables d'environnement
load_dotenv()

# Configuration de la base de données
DATABASE_URL = os.getenv("DATABASE_URL", "postgresql://c2ew_user:c2ew_secure_password_2024@localhost:5432/c2ew_db")
JWT_SECRET_KEY = os.getenv("JWT_SECRET_KEY", "c2ew_super_secret_key_2024_very_secure")
JWT_ALGORITHM = os.getenv("JWT_ALGORITHM", "HS256")

# Modèles Pydantic pour les API
class LoginRequest(BaseModel):
    username: str
    password: str

class Token(BaseModel):
    access_token: str
    token_type: str

class User(BaseModel):
    id: int
    username: str
    email: str
    full_name: str
    role: str
    is_active: bool

class Equipment(BaseModel):
    id: int
    name: str
    type: str
    status: str
    description: Optional[str] = None
    latitude: Optional[float] = None
    longitude: Optional[float] = None
    altitude: Optional[float] = None
    configuration: Optional[Dict[str, Any]] = None
    metrics: Optional[Dict[str, Any]] = None

class Alert(BaseModel):
    id: int
    title: str
    message: str
    level: str
    status: str
    equipment_id: Optional[int] = None
    latitude: Optional[float] = None
    longitude: Optional[float] = None
    metadata: Optional[Dict[str, Any]] = None
    created_at: datetime

# Configuration de la base de données
engine = create_engine(DATABASE_URL, echo=True)
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)

def get_session():
    session = SessionLocal()
    try:
        yield session
    finally:
        session.close()

# Gestionnaire WebSocket
class ConnectionManager:
    def __init__(self):
        self.active_connections: List[WebSocket] = []

    async def connect(self, websocket: WebSocket):
        await websocket.accept()
        self.active_connections.append(websocket)

    def disconnect(self, websocket: WebSocket):
        if websocket in self.active_connections:
            self.active_connections.remove(websocket)

    async def send_personal_message(self, message: str, websocket: WebSocket):
        try:
            await websocket.send_text(message)
        except:
            self.disconnect(websocket)

    async def broadcast(self, message: str):
        for connection in self.active_connections[:]:  # Copie pour éviter les modifications pendant l'itération
            try:
                await connection.send_text(message)
            except:
                self.disconnect(connection)

manager = ConnectionManager()

# Fonctions utilitaires
def create_access_token(data: dict, expires_delta: Optional[timedelta] = None):
    to_encode = data.copy()
    if expires_delta:
        expire = datetime.utcnow() + expires_delta
    else:
        expire = datetime.utcnow() + timedelta(minutes=30)
    to_encode.update({"exp": expire})
    encoded_jwt = jwt.encode(to_encode, JWT_SECRET_KEY, algorithm=JWT_ALGORITHM)
    return encoded_jwt

def verify_token(token: str):
    try:
        payload = jwt.decode(token, JWT_SECRET_KEY, algorithms=[JWT_ALGORITHM])
        username: str = payload.get("sub")
        if username is None:
            raise HTTPException(status_code=401, detail="Token invalide")
        return username
    except jwt.PyJWTError:
        raise HTTPException(status_code=401, detail="Token invalide")

# Sécurité
security = HTTPBearer()

async def get_current_user(credentials: HTTPAuthorizationCredentials = Depends(security), session = Depends(get_session)):
    username = verify_token(credentials.credentials)

    # Requête SQL brute pour récupérer l'utilisateur
    result = session.execute(
        text("SELECT id, username, email, full_name, role, is_active FROM c2ew.users WHERE username = :username AND is_active = true"),
        {"username": username}
    )
    user_data = result.fetchone()

    if not user_data:
        raise HTTPException(status_code=401, detail="Utilisateur non trouvé")

    return User(
        id=user_data[0],
        username=user_data[1],
        email=user_data[2],
        full_name=user_data[3],
        role=user_data[4],
        is_active=user_data[5]
    )

# Gestionnaire de cycle de vie de l'application
@asynccontextmanager
async def lifespan(app: FastAPI):
    print("🚀 Démarrage du serveur backend C2-EW avec PostgreSQL...")
    print(f"📡 API disponible sur: http://localhost:8000")
    print(f"📚 Documentation sur: http://localhost:8000/docs")
    print(f"🗄️  Base de données: {DATABASE_URL.split('@')[1] if '@' in DATABASE_URL else 'PostgreSQL'}")
    yield
    print("🛑 Arrêt du serveur backend C2-EW")

# Application FastAPI
app = FastAPI(
    title="C2-EW Platform API",
    description="API pour la plateforme C2-EW avec support PostgreSQL/PostGIS",
    version="1.0.0",
    lifespan=lifespan
)

# Configuration CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["http://localhost:3000", "http://127.0.0.1:3000"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Routes d'authentification
@app.post("/api/v1/auth/login", response_model=Token)
async def login(login_data: LoginRequest, session = Depends(get_session)):
    # Vérifier les identifiants avec bcrypt
    result = session.execute(
        text("SELECT id, username, password_hash, role, is_active FROM c2ew.users WHERE username = :username AND is_active = true"),
        {"username": login_data.username}
    )
    user_data = result.fetchone()

    if not user_data:
        raise HTTPException(status_code=401, detail="Identifiants incorrects")

    # Vérifier le mot de passe avec crypt
    password_check = session.execute(
        text("SELECT crypt(:password, :hash) = :hash as password_valid"),
        {"password": login_data.password, "hash": user_data[2]}
    )
    is_valid = password_check.fetchone()[0]

    if not is_valid:
        raise HTTPException(status_code=401, detail="Identifiants incorrects")

    # Mettre à jour la dernière connexion
    session.execute(
        text("UPDATE c2ew.users SET last_login = CURRENT_TIMESTAMP WHERE id = :user_id"),
        {"user_id": user_data[0]}
    )
    session.commit()

    access_token = create_access_token(data={"sub": user_data[1]})
    return {"access_token": access_token, "token_type": "bearer"}

@app.get("/api/v1/auth/me", response_model=User)
async def get_current_user_info(current_user: User = Depends(get_current_user)):
    return current_user

@app.post("/api/v1/auth/refresh")
async def refresh_token():
    raise HTTPException(status_code=501, detail="Refresh token non implémenté")

@app.post("/api/v1/auth/logout")
async def logout():
    return {"message": "Déconnexion réussie"}

# Routes des équipements
@app.get("/api/v1/equipment", response_model=List[Equipment])
async def get_equipment(session = Depends(get_session), current_user: User = Depends(get_current_user)):
    result = session.execute(text("""
        SELECT id, name, type, status, description,
               ST_Y(location) as latitude, ST_X(location) as longitude,
               altitude, configuration, metrics
        FROM c2ew.equipment
        ORDER BY name
    """))

    equipment_list = []
    for row in result.fetchall():
        equipment_list.append(Equipment(
            id=row[0],
            name=row[1],
            type=row[2],
            status=row[3],
            description=row[4],
            latitude=row[5],
            longitude=row[6],
            altitude=row[7],
            configuration=row[8],
            metrics=row[9]
        ))

    return equipment_list

# Routes des alertes
@app.get("/api/v1/alerts", response_model=List[Alert])
async def get_alerts(session = Depends(get_session), current_user: User = Depends(get_current_user)):
    result = session.execute(text("""
        SELECT id, title, message, level, status, equipment_id,
               ST_Y(location) as latitude, ST_X(location) as longitude,
               metadata, created_at
        FROM c2ew.alerts
        ORDER BY created_at DESC
        LIMIT 100
    """))

    alerts_list = []
    for row in result.fetchall():
        alerts_list.append(Alert(
            id=row[0],
            title=row[1],
            message=row[2],
            level=row[3],
            status=row[4],
            equipment_id=row[5],
            latitude=row[6],
            longitude=row[7],
            metadata=row[8],
            created_at=row[9]
        ))

    return alerts_list

# Route WebSocket
@app.websocket("/ws")
async def websocket_endpoint(websocket: WebSocket, token: str = Query(None)):
    await manager.connect(websocket)
    try:
        # Envoyer un message de bienvenue
        await manager.send_personal_message(json.dumps({
            "type": "connection_established",
            "data": {
                "message": "Connexion WebSocket établie avec PostgreSQL",
                "timestamp": datetime.utcnow().isoformat(),
                "database": "PostgreSQL/PostGIS"
            }
        }), websocket)
        
        while True:
            # Recevoir les messages du client
            data = await websocket.receive_text()
            message = json.loads(data)
            
            # Echo du message
            await manager.send_personal_message(json.dumps({
                "type": "echo",
                "data": message,
                "timestamp": datetime.utcnow().isoformat()
            }), websocket)
            
    except WebSocketDisconnect:
        manager.disconnect(websocket)

# Route racine
@app.get("/")
async def root():
    return {
        "message": "C2-EW Platform API avec PostgreSQL/PostGIS",
        "version": "1.0.0",
        "database": "PostgreSQL/PostGIS",
        "docs": "/docs"
    }

# Route de santé
@app.get("/health")
async def health_check(session = Depends(get_session)):
    try:
        # Test de la base de données
        result = session.execute(text("SELECT 1"))
        db_status = "OK" if result.fetchone() else "ERROR"

        # Test PostGIS
        postgis_result = session.execute(text("SELECT PostGIS_Version()"))
        postgis_version = postgis_result.fetchone()[0]

        return {
            "status": "healthy",
            "database": db_status,
            "postgis": postgis_version,
            "timestamp": datetime.utcnow().isoformat()
        }
    except Exception as e:
        return {
            "status": "unhealthy",
            "error": str(e),
            "timestamp": datetime.utcnow().isoformat()
        }

if __name__ == "__main__":
    uvicorn.run(
        "database_server:app",
        host="0.0.0.0",
        port=8000,
        reload=True,
        log_level="info"
    )
