<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Carte Offline Maroc - WebWorldWind</title>
    <style>
        body {
            margin: 0;
            padding: 0;
            background-color: #1a1a1a;
            color: white;
            font-family: Arial, sans-serif;
        }
        
        .container {
            width: 100vw;
            height: 100vh;
            display: flex;
            flex-direction: column;
        }
        
        .header {
            background-color: #2d5a27;
            padding: 15px;
            border-bottom: 1px solid #4a7c59;
        }
        
        .status {
            background-color: #2a2a2a;
            padding: 10px 15px;
            border-bottom: 1px solid #555;
            font-size: 14px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .map-container {
            flex: 1;
            position: relative;
            background-color: #0a0a0a;
        }
        
        #worldWindCanvas {
            width: 100%;
            height: 100%;
            display: block;
        }
        
        .loading {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            text-align: center;
            background-color: rgba(0,0,0,0.8);
            padding: 30px;
            border-radius: 10px;
        }
        
        .spinner {
            border: 3px solid #333;
            border-top: 3px solid #4CAF50;
            border-radius: 50%;
            width: 40px;
            height: 40px;
            animation: spin 1s linear infinite;
            margin: 0 auto 20px;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        .controls {
            position: absolute;
            top: 20px;
            right: 20px;
            background-color: rgba(0,0,0,0.9);
            padding: 20px;
            border-radius: 10px;
            min-width: 250px;
            border: 2px solid #4CAF50;
        }
        
        .btn {
            background-color: #4CAF50;
            color: white;
            border: none;
            padding: 10px 16px;
            margin: 5px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 14px;
            width: 100%;
        }
        
        .btn:hover {
            background-color: #45a049;
        }
        
        .btn:disabled {
            background-color: #666;
            cursor: not-allowed;
        }
        
        .btn-2d { background-color: #2196F3; }
        .btn-2d:hover { background-color: #1976D2; }
        
        .btn-3d { background-color: #FF9800; }
        .btn-3d:hover { background-color: #F57C00; }
        
        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }
        
        .status-ok { background-color: #4CAF50; }
        .status-error { background-color: #F44336; }
        .status-loading { background-color: #FF9800; }
        
        .offline-badge {
            background-color: #4CAF50;
            color: white;
            padding: 5px 10px;
            border-radius: 15px;
            font-size: 12px;
            font-weight: bold;
        }
        
        .data-info {
            margin-top: 15px;
            padding: 10px;
            background-color: rgba(76, 175, 80, 0.1);
            border-radius: 5px;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🗺️ Carte Offline du Maroc - WebWorldWind</h1>
            <p>Carte entièrement offline avec données locales</p>
        </div>
        
        <div class="status">
            <div>
                <span id="statusIndicator" class="status-indicator status-loading"></span>
                <span id="statusText">Initialisation carte offline...</span>
            </div>
            <div class="offline-badge">
                📡 OFFLINE
            </div>
        </div>
        
        <div class="map-container">
            <canvas id="worldWindCanvas" width="1024" height="768">
                Votre navigateur ne supporte pas HTML5 Canvas.
            </canvas>
            
            <div id="loadingDiv" class="loading">
                <div class="spinner"></div>
                <div>Chargement carte offline...</div>
                <div id="loadingDetails" style="font-size: 12px; color: #ccc; margin-top: 10px;">
                    Vérification des données locales...
                </div>
            </div>
            
            <div class="controls">
                <h3>🎛️ Contrôles</h3>
                
                <div style="margin-bottom: 15px;">
                    <button id="btn2D" class="btn btn-2d" onclick="switchTo2D()">
                        📐 Mode 2D
                    </button>
                    <button id="btn3D" class="btn btn-3d" onclick="switchTo3D()">
                        🌍 Mode 3D
                    </button>
                </div>
                
                <button id="btnMorocco" class="btn" onclick="zoomToMorocco()" disabled>
                    🇲🇦 Centrer Maroc
                </button>
                <button id="btnRegion" class="btn" onclick="zoomToRegion()" disabled>
                    🌍 Vue Région
                </button>
                <button id="btnReload" class="btn" onclick="reloadMap()">
                    🔄 Recharger
                </button>
                
                <div class="data-info">
                    <h4>📊 État des données</h4>
                    <div>Config: <span id="configStatus">❌</span></div>
                    <div>Imagerie: <span id="imageryStatus">❌</span></div>
                    <div>Élévation: <span id="elevationStatus">❌</span></div>
                    <div>WebWorldWind: <span id="wwStatus">❌</span></div>
                </div>
                
                <div style="margin-top: 10px; font-size: 11px; color: #ccc;">
                    Mode actuel: <span id="currentMode">2D</span><br>
                    Zoom: <span id="currentZoom">-</span><br>
                    Position: <span id="currentPos">-</span>
                </div>
            </div>
        </div>
    </div>

    <!-- WebWorldWind Library - LOCAL OFFLINE VERSION -->
    <script src="/worldwind.min.js"
            onload="console.log('✅ WebWorldWind loaded OFFLINE'); initializeOfflineMap();"
            onerror="console.error('❌ Failed to load WebWorldWind OFFLINE'); showError('Impossible de charger WebWorldWind offline');">
    </script>

    <script>
        let worldWindow = null;
        let currentMode = '2D';
        let offlineConfig = null;

        function updateStatus(text, type = 'loading') {
            const indicator = document.getElementById('statusIndicator');
            const statusText = document.getElementById('statusText');
            
            indicator.className = `status-indicator status-${type}`;
            statusText.textContent = text;
            
            console.log(`Status: ${text}`);
        }

        function updateDataStatus(component, status) {
            const element = document.getElementById(component + 'Status');
            if (element) {
                element.textContent = status ? '✅' : '❌';
            }
        }

        function showError(message) {
            updateStatus(`Erreur: ${message}`, 'error');
            document.getElementById('loadingDetails').textContent = message;
        }

        function hideLoading() {
            document.getElementById('loadingDiv').style.display = 'none';
            document.getElementById('btnMorocco').disabled = false;
            document.getElementById('btnRegion').disabled = false;
        }

        async function loadOfflineConfig() {
            try {
                updateStatus('Chargement configuration offline...', 'loading');
                const response = await fetch('/worldwind-data/config/morocco-layers.json');
                
                if (response.ok) {
                    offlineConfig = await response.json();
                    updateDataStatus('config', true);
                    console.log('✅ Configuration offline chargée:', offlineConfig);
                    return true;
                } else {
                    throw new Error('Configuration non trouvée');
                }
            } catch (error) {
                console.warn('⚠️ Configuration offline non disponible:', error);
                updateDataStatus('config', false);
                
                // Configuration par défaut
                offlineConfig = {
                    region: {
                        center: { latitude: 31.7917, longitude: -7.0926 },
                        bounds: { north: 40.0, south: 15.0, east: 10.0, west: -20.0 }
                    }
                };
                return false;
            }
        }

        async function checkOfflineData() {
            updateStatus('Vérification des données offline...', 'loading');
            
            // Vérifier les tiles d'imagerie
            try {
                const testTile = await fetch('/worldwind-data/imagery/5/16/11.png');
                updateDataStatus('imagery', testTile.ok);
            } catch {
                updateDataStatus('imagery', false);
            }
            
            // Vérifier les données d'élévation
            try {
                const elevationMeta = await fetch('/worldwind-data/elevation/metadata.json');
                updateDataStatus('elevation', elevationMeta.ok);
            } catch {
                updateDataStatus('elevation', false);
            }
        }

        async function initializeOfflineMap() {
            try {
                if (typeof WorldWind === 'undefined') {
                    throw new Error('WebWorldWind non disponible');
                }
                
                updateDataStatus('ww', true);
                
                // Charger la configuration
                await loadOfflineConfig();
                await checkOfflineData();
                
                updateStatus('Création du WorldWindow...', 'loading');
                
                const canvas = document.getElementById('worldWindCanvas');
                worldWindow = new WorldWind.WorldWindow(canvas);
                
                // Configuration initiale
                worldWindow.navigator.lookAtLocation.latitude = offlineConfig.region.center.latitude;
                worldWindow.navigator.lookAtLocation.longitude = offlineConfig.region.center.longitude;
                worldWindow.navigator.range = 2000000;
                
                // Mode 2D par défaut
                worldWindow.globe = new WorldWind.Globe2D();
                
                updateStatus('Ajout des couches...', 'loading');
                addLayers();
                
                updateStatus('Carte offline prête!', 'ok');
                hideLoading();
                
                // Mise à jour des infos
                updateMapInfo();
                
                console.log('✅ Carte offline initialisée avec succès');
                
            } catch (error) {
                console.error('❌ Erreur initialisation carte offline:', error);
                showError(`Erreur: ${error.message}`);
            }
        }

        function addLayers() {
            try {
                // Essayer d'abord les couches offline, sinon fallback
                
                // Couche d'imagerie (offline ou fallback)
                const bmngLayer = new WorldWind.BMNGOneImageLayer();
                worldWindow.addLayer(bmngLayer);
                
                // Couches de contrôle
                const compassLayer = new WorldWind.CompassLayer();
                worldWindow.addLayer(compassLayer);

                const coordsLayer = new WorldWind.CoordinatesDisplayLayer(worldWindow);
                worldWindow.addLayer(coordsLayer);

                const controlsLayer = new WorldWind.ViewControlsLayer(worldWindow);
                worldWindow.addLayer(controlsLayer);

                console.log('✅ Couches ajoutées');
            } catch (error) {
                console.error('❌ Erreur ajout couches:', error);
                throw error;
            }
        }

        function switchTo2D() {
            if (worldWindow) {
                worldWindow.globe = new WorldWind.Globe2D();
                currentMode = '2D';
                updateMapInfo();
                updateStatus('Mode 2D activé', 'ok');
            }
        }

        function switchTo3D() {
            if (worldWindow) {
                worldWindow.globe = new WorldWind.Globe();
                currentMode = '3D';
                updateMapInfo();
                updateStatus('Mode 3D activé', 'ok');
            }
        }

        function zoomToMorocco() {
            if (worldWindow) {
                worldWindow.navigator.lookAtLocation.latitude = 31.7917;
                worldWindow.navigator.lookAtLocation.longitude = -7.0926;
                worldWindow.navigator.range = 1000000;
                worldWindow.redraw();
                updateMapInfo();
                updateStatus('Vue centrée sur le Maroc', 'ok');
            }
        }

        function zoomToRegion() {
            if (worldWindow) {
                worldWindow.navigator.lookAtLocation.latitude = 28.0;
                worldWindow.navigator.lookAtLocation.longitude = -5.0;
                worldWindow.navigator.range = 3000000;
                worldWindow.redraw();
                updateMapInfo();
                updateStatus('Vue élargie sur la région', 'ok');
            }
        }

        function updateMapInfo() {
            if (worldWindow) {
                document.getElementById('currentMode').textContent = currentMode;
                document.getElementById('currentZoom').textContent = Math.round(Math.log2(40075000 / worldWindow.navigator.range));
                document.getElementById('currentPos').textContent = 
                    `${worldWindow.navigator.lookAtLocation.latitude.toFixed(2)}°, ${worldWindow.navigator.lookAtLocation.longitude.toFixed(2)}°`;
            }
        }

        function reloadMap() {
            location.reload();
        }

        // Initialisation
        window.addEventListener('load', function() {
            updateStatus('Initialisation...', 'loading');
            
            if (typeof WorldWind === 'undefined') {
                setTimeout(() => {
                    if (typeof WorldWind === 'undefined') {
                        showError('WebWorldWind non chargé après 5 secondes');
                    }
                }, 5000);
            }
        });
    </script>
</body>
</html>
