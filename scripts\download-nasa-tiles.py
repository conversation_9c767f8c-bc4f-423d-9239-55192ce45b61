#!/usr/bin/env python3
"""
Script pour télécharger les tiles NASA WorldWind pour le Maroc
Télécharge les images satellite depuis les serveurs NASA GIBS
"""

import os
import sys
import requests
import json
import time
import math
from pathlib import Path
from urllib.parse import urljoin
import concurrent.futures
from datetime import datetime, timedelta

# Configuration pour le Maroc
MOROCCO_BOUNDS = {
    'north': 36.0,   # Nord du Maroc + marge
    'south': 20.0,   # Sud du Maroc + marge  
    'east': 2.0,     # Est du Maroc + marge
    'west': -18.0    # Ouest du Maroc + marge (Océan Atlantique)
}

# Configuration des serveurs NASA
NASA_SERVERS = {
    'GIBS': 'https://gibs.earthdata.nasa.gov/wmts/epsg3857/best',
    'WORLDWIND': 'https://files.worldwind.arc.nasa.gov/artifactory/web/0.11.0'
}

# Couches disponibles
LAYERS = {
    'MODIS_Terra': {
        'name': 'MODIS_Terra_CorrectedReflectance_TrueColor',
        'format': 'jpg',
        'description': 'Images satellite MODIS Terra en couleurs vraies'
    },
    'MODIS_Aqua': {
        'name': 'MODIS_Aqua_CorrectedReflectance_TrueColor', 
        'format': 'jpg',
        'description': 'Images satellite MODIS Aqua en couleurs vraies'
    },
    'VIIRS': {
        'name': 'VIIRS_SNPP_CorrectedReflectance_TrueColor',
        'format': 'jpg',
        'description': 'Images satellite VIIRS en couleurs vraies'
    },
    'BlueMarble': {
        'name': 'BlueMarble_ShadedRelief_Bathymetry',
        'format': 'jpg',
        'description': 'Blue Marble avec relief ombré'
    }
}

# Dossier de destination
TILES_DIR = Path(__file__).parent.parent / "frontend" / "public" / "tiles" / "morocco"

def deg2num(lat_deg, lon_deg, zoom):
    """Convertit lat/lon en coordonnées de tile (format Web Mercator)"""
    lat_rad = math.radians(lat_deg)
    n = 2.0 ** zoom
    xtile = int((lon_deg + 180.0) / 360.0 * n)
    ytile = int((1.0 - math.asinh(math.tan(lat_rad)) / math.pi) / 2.0 * n)
    return (xtile, ytile)

def num2deg(xtile, ytile, zoom):
    """Convertit coordonnées de tile en lat/lon"""
    n = 2.0 ** zoom
    lon_deg = xtile / n * 360.0 - 180.0
    lat_rad = math.atan(math.sinh(math.pi * (1 - 2 * ytile / n)))
    lat_deg = math.degrees(lat_rad)
    return (lat_deg, lon_deg)

def get_tile_bounds(zoom_level):
    """Calcule les limites des tiles pour le Maroc à un niveau de zoom donné"""
    min_x, max_y = deg2num(MOROCCO_BOUNDS['north'], MOROCCO_BOUNDS['west'], zoom_level)
    max_x, min_y = deg2num(MOROCCO_BOUNDS['south'], MOROCCO_BOUNDS['east'], zoom_level)
    
    return {
        'min_x': min_x,
        'max_x': max_x,
        'min_y': min_y,
        'max_y': max_y,
        'total': (max_x - min_x + 1) * (max_y - min_y + 1)
    }

def build_tile_url(layer, zoom, x, y, date=None):
    """Construit l'URL d'une tile NASA GIBS"""
    if date is None:
        # Utiliser une date récente (7 jours en arrière pour éviter les données manquantes)
        date = (datetime.now() - timedelta(days=7)).strftime('%Y-%m-%d')
    
    layer_info = LAYERS[layer]
    base_url = NASA_SERVERS['GIBS']
    
    # Format URL GIBS: /layer/tilematrixset/time/tilematrix/tilerow/tilecol.format
    url = f"{base_url}/{layer_info['name']}/GoogleMapsCompatible_Level9/{date}/{zoom}/{y}/{x}.{layer_info['format']}"
    
    return url

def download_tile(url, filepath, max_retries=3):
    """Télécharge une tile avec gestion des erreurs et retry"""
    for attempt in range(max_retries):
        try:
            headers = {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
            }
            
            response = requests.get(url, headers=headers, timeout=30)
            
            if response.status_code == 200:
                # Créer le dossier si nécessaire
                filepath.parent.mkdir(parents=True, exist_ok=True)
                
                # Sauvegarder la tile
                with open(filepath, 'wb') as f:
                    f.write(response.content)
                
                return True, len(response.content)
            
            elif response.status_code == 404:
                # Tile non disponible (normal pour certaines zones/dates)
                return False, 0
            
            else:
                print(f"❌ Erreur HTTP {response.status_code} pour {url}")
                
        except requests.exceptions.RequestException as e:
            print(f"⚠️ Tentative {attempt + 1}/{max_retries} échouée pour {url}: {e}")
            if attempt < max_retries - 1:
                time.sleep(2 ** attempt)  # Backoff exponentiel
    
    return False, 0

def download_layer_tiles(layer, zoom_levels, max_workers=5):
    """Télécharge toutes les tiles d'une couche pour les niveaux de zoom spécifiés"""
    print(f"\n🛰️ Téléchargement de la couche {layer}")
    print(f"📋 Description: {LAYERS[layer]['description']}")
    
    total_tiles = 0
    downloaded_tiles = 0
    total_size = 0
    
    # Calculer le nombre total de tiles
    for zoom in zoom_levels:
        bounds = get_tile_bounds(zoom)
        total_tiles += bounds['total']
        print(f"   Zoom {zoom}: {bounds['total']} tiles")
    
    print(f"📊 Total à télécharger: {total_tiles} tiles")
    
    # Préparer les tâches de téléchargement
    download_tasks = []
    
    for zoom in zoom_levels:
        bounds = get_tile_bounds(zoom)
        
        for x in range(bounds['min_x'], bounds['max_x'] + 1):
            for y in range(bounds['min_y'], bounds['max_y'] + 1):
                url = build_tile_url(layer, zoom, x, y)
                filepath = TILES_DIR / str(zoom) / str(x) / f"{y}.{LAYERS[layer]['format']}"
                
                # Skip si le fichier existe déjà
                if not filepath.exists():
                    download_tasks.append((url, filepath))
    
    print(f"📥 {len(download_tasks)} nouvelles tiles à télécharger")
    
    if not download_tasks:
        print("✅ Toutes les tiles sont déjà téléchargées")
        return
    
    # Téléchargement parallèle
    start_time = time.time()
    
    with concurrent.futures.ThreadPoolExecutor(max_workers=max_workers) as executor:
        future_to_task = {
            executor.submit(download_tile, url, filepath): (url, filepath)
            for url, filepath in download_tasks
        }
        
        for i, future in enumerate(concurrent.futures.as_completed(future_to_task)):
            url, filepath = future_to_task[future]
            
            try:
                success, size = future.result()
                if success:
                    downloaded_tiles += 1
                    total_size += size
                
                # Afficher le progrès
                if (i + 1) % 50 == 0 or (i + 1) == len(download_tasks):
                    elapsed = time.time() - start_time
                    rate = (i + 1) / elapsed if elapsed > 0 else 0
                    eta = (len(download_tasks) - i - 1) / rate if rate > 0 else 0
                    
                    print(f"📊 Progrès: {i + 1}/{len(download_tasks)} "
                          f"({downloaded_tiles} réussies) "
                          f"- {rate:.1f} tiles/s "
                          f"- ETA: {eta/60:.1f}min")
                
            except Exception as e:
                print(f"❌ Erreur inattendue pour {url}: {e}")
    
    elapsed = time.time() - start_time
    size_mb = total_size / (1024 * 1024)
    
    print(f"\n✅ Téléchargement terminé pour {layer}")
    print(f"📊 {downloaded_tiles}/{len(download_tasks)} tiles téléchargées")
    print(f"💾 Taille: {size_mb:.1f} MB")
    print(f"⏱️ Temps: {elapsed/60:.1f} minutes")

def create_tile_index():
    """Crée un index des tiles téléchargées"""
    index = {
        'name': 'Morocco NASA Tiles',
        'description': 'Tiles satellite NASA pour le Maroc',
        'bounds': MOROCCO_BOUNDS,
        'generated': datetime.now().isoformat(),
        'layers': {},
        'zoom_levels': {},
        'total_tiles': 0,
        'total_size': 0
    }
    
    # Parcourir les tiles téléchargées
    if TILES_DIR.exists():
        for zoom_dir in TILES_DIR.iterdir():
            if zoom_dir.is_dir() and zoom_dir.name.isdigit():
                zoom = int(zoom_dir.name)
                tile_count = 0
                total_size = 0
                
                for x_dir in zoom_dir.iterdir():
                    if x_dir.is_dir():
                        for tile_file in x_dir.iterdir():
                            if tile_file.is_file():
                                tile_count += 1
                                total_size += tile_file.stat().st_size
                
                index['zoom_levels'][zoom] = {
                    'tiles': tile_count,
                    'size': total_size
                }
                
                index['total_tiles'] += tile_count
                index['total_size'] += total_size
    
    # Sauvegarder l'index
    index_file = TILES_DIR / 'index.json'
    index_file.parent.mkdir(parents=True, exist_ok=True)
    
    with open(index_file, 'w', encoding='utf-8') as f:
        json.dump(index, f, indent=2, ensure_ascii=False)
    
    print(f"📋 Index créé: {index_file}")
    return index

def main():
    """Fonction principale"""
    print("🛰️ Téléchargeur de tiles NASA pour le Maroc")
    print(f"📁 Dossier de destination: {TILES_DIR}")
    
    # Configuration par défaut
    layers_to_download = ['MODIS_Terra', 'BlueMarble']  # Couches les plus fiables
    zoom_levels = [5, 6, 7, 8, 9]  # Du général au détaillé
    
    print(f"\n📋 Configuration:")
    print(f"   Couches: {', '.join(layers_to_download)}")
    print(f"   Niveaux de zoom: {zoom_levels}")
    print(f"   Zone: {MOROCCO_BOUNDS}")
    
    # Calculer l'estimation
    total_estimate = 0
    for zoom in zoom_levels:
        bounds = get_tile_bounds(zoom)
        total_estimate += bounds['total']
    
    print(f"\n📊 Estimation: ~{total_estimate} tiles (~{total_estimate * 50 / 1024:.1f} MB)")
    
    # Confirmation
    response = input("\n❓ Continuer le téléchargement ? (o/N): ")
    if response.lower() not in ['o', 'oui', 'y', 'yes']:
        print("❌ Téléchargement annulé")
        return
    
    # Créer le dossier de destination
    TILES_DIR.mkdir(parents=True, exist_ok=True)
    
    # Télécharger chaque couche
    start_time = time.time()
    
    for layer in layers_to_download:
        try:
            download_layer_tiles(layer, zoom_levels)
        except KeyboardInterrupt:
            print("\n⚠️ Téléchargement interrompu par l'utilisateur")
            break
        except Exception as e:
            print(f"❌ Erreur lors du téléchargement de {layer}: {e}")
            continue
    
    # Créer l'index final
    print("\n📋 Création de l'index des tiles...")
    index = create_tile_index()
    
    total_time = time.time() - start_time
    size_mb = index['total_size'] / (1024 * 1024)
    
    print(f"\n🎉 Téléchargement terminé !")
    print(f"📊 {index['total_tiles']} tiles téléchargées")
    print(f"💾 Taille totale: {size_mb:.1f} MB")
    print(f"⏱️ Temps total: {total_time/60:.1f} minutes")
    print(f"\n🗺️ Les tiles sont prêtes pour utilisation offline !")
    print(f"📁 Emplacement: {TILES_DIR}")

if __name__ == "__main__":
    main()
