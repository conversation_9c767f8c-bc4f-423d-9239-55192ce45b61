#!/usr/bin/env python3
"""
Script pour télécharger les données offline de la carte du Maroc
- Images satellite (Landsat/Sentinel)
- Données d'élévation (SRTM)
- Génération des tiles pour WebWorldWind
"""

import os
import sys
import requests
import json
from pathlib import Path
import math
from PIL import Image
import numpy as np

# Configuration
MOROCCO_BOUNDS = {
    'north': 40.0,
    'south': 15.0, 
    'east': 10.0,
    'west': -20.0
}

TILE_SIZE = 256
MIN_ZOOM = 5
MAX_ZOOM = 11

# Dossiers de destination
BASE_DIR = Path(__file__).parent.parent / "frontend" / "public" / "worldwind-data"
IMAGERY_DIR = BASE_DIR / "imagery"
ELEVATION_DIR = BASE_DIR / "elevation"

def deg2num(lat_deg, lon_deg, zoom):
    """Convertit lat/lon en coordonnées de tile"""
    lat_rad = math.radians(lat_deg)
    n = 2.0 ** zoom
    xtile = int((lon_deg + 180.0) / 360.0 * n)
    ytile = int((1.0 - math.asinh(math.tan(lat_rad)) / math.pi) / 2.0 * n)
    return (xtile, ytile)

def num2deg(xtile, ytile, zoom):
    """Convertit coordonnées de tile en lat/lon"""
    n = 2.0 ** zoom
    lon_deg = xtile / n * 360.0 - 180.0
    lat_rad = math.atan(math.sinh(math.pi * (1 - 2 * ytile / n)))
    lat_deg = math.degrees(lat_rad)
    return (lat_deg, lon_deg)

def create_sample_tiles():
    """Crée des tiles d'exemple pour tester la structure"""
    print("🎨 Création de tiles d'exemple...")
    
    for zoom in range(MIN_ZOOM, MAX_ZOOM + 1):
        # Calculer les tiles nécessaires pour le Maroc
        min_x, max_y = deg2num(MOROCCO_BOUNDS['north'], MOROCCO_BOUNDS['west'], zoom)
        max_x, min_y = deg2num(MOROCCO_BOUNDS['south'], MOROCCO_BOUNDS['east'], zoom)
        
        zoom_dir = IMAGERY_DIR / str(zoom)
        zoom_dir.mkdir(parents=True, exist_ok=True)
        
        print(f"  Zoom {zoom}: {max_x - min_x + 1} x {max_y - min_y + 1} tiles")
        
        # Créer quelques tiles d'exemple
        sample_count = min(10, (max_x - min_x + 1) * (max_y - min_y + 1))
        
        for i in range(sample_count):
            x = min_x + (i % (max_x - min_x + 1))
            y = min_y + (i // (max_x - min_x + 1))
            
            x_dir = zoom_dir / str(x)
            x_dir.mkdir(exist_ok=True)
            
            # Créer une tile d'exemple (couleur basée sur la position)
            img = create_sample_tile_image(x, y, zoom)
            tile_path = x_dir / f"{y}.png"
            img.save(tile_path)
        
        print(f"  ✅ {sample_count} tiles créées pour le zoom {zoom}")

def create_sample_tile_image(x, y, zoom):
    """Crée une image de tile d'exemple"""
    # Couleur basée sur la position géographique
    lat, lon = num2deg(x, y, zoom)
    
    # Couleur basée sur la latitude (bleu pour océan, vert/marron pour terre)
    if lon < -10:  # Océan Atlantique
        color = (50, 100, 200)  # Bleu océan
    elif lat > 35:  # Nord (Espagne)
        color = (100, 150, 100)  # Vert
    elif lat < 20:  # Sud (Mauritanie)
        color = (200, 180, 120)  # Beige désert
    else:  # Maroc
        color = (150, 120, 80)   # Marron/terre
    
    # Créer l'image
    img = Image.new('RGB', (TILE_SIZE, TILE_SIZE), color)
    
    # Ajouter du bruit pour simuler le terrain
    pixels = np.array(img)
    noise = np.random.randint(-20, 20, (TILE_SIZE, TILE_SIZE, 3))
    pixels = np.clip(pixels + noise, 0, 255)
    
    return Image.fromarray(pixels.astype('uint8'))

def create_sample_elevation_data():
    """Crée des données d'élévation d'exemple"""
    print("🏔️ Création de données d'élévation d'exemple...")
    
    # Créer un fichier d'élévation simple (format texte pour l'exemple)
    elevation_file = ELEVATION_DIR / "morocco-dem-sample.txt"
    
    with open(elevation_file, 'w') as f:
        f.write("# Données d'élévation d'exemple pour le Maroc\n")
        f.write("# Format: latitude,longitude,elevation\n")
        
        # Quelques points d'élévation d'exemple
        sample_points = [
            (31.7917, -7.0926, 500),    # Casablanca
            (33.9716, -6.8498, 200),    # Rabat
            (31.2001, -7.9218, 4167),   # Mont Toubkal (point culminant)
            (35.7595, -5.8340, 100),    # Tanger
            (31.9454, -4.4142, 800),    # Fès
        ]
        
        for lat, lon, elev in sample_points:
            f.write(f"{lat},{lon},{elev}\n")
    
    print(f"  ✅ Fichier d'élévation créé: {elevation_file}")

def create_tile_structure_info():
    """Crée un fichier d'information sur la structure des tiles"""
    info = {
        "name": "Morocco Offline Tiles",
        "description": "Tiles d'exemple pour la carte offline du Maroc",
        "bounds": MOROCCO_BOUNDS,
        "tileSize": TILE_SIZE,
        "minZoom": MIN_ZOOM,
        "maxZoom": MAX_ZOOM,
        "format": "png",
        "scheme": "xyz",
        "generated": "2024-01-01",
        "zoomLevels": {}
    }
    
    for zoom in range(MIN_ZOOM, MAX_ZOOM + 1):
        min_x, max_y = deg2num(MOROCCO_BOUNDS['north'], MOROCCO_BOUNDS['west'], zoom)
        max_x, min_y = deg2num(MOROCCO_BOUNDS['south'], MOROCCO_BOUNDS['east'], zoom)
        
        info["zoomLevels"][str(zoom)] = {
            "tileCount": f"{max_x - min_x + 1}x{max_y - min_y + 1}",
            "xRange": [min_x, max_x],
            "yRange": [min_y, max_y],
            "resolution": f"~{40075000 / (2**zoom * TILE_SIZE):.0f}m"
        }
    
    info_file = IMAGERY_DIR / "tile-info.json"
    with open(info_file, 'w', encoding='utf-8') as f:
        json.dump(info, f, indent=2, ensure_ascii=False)
    
    print(f"  ✅ Info tiles créée: {info_file}")

def download_real_data():
    """Instructions pour télécharger de vraies données"""
    print("\n📥 Pour télécharger de vraies données:")
    print("1. Images satellite:")
    print("   - USGS Earth Explorer: https://earthexplorer.usgs.gov/")
    print("   - Copernicus Open Access Hub: https://scihub.copernicus.eu/")
    print("   - Rechercher: Landsat 8/9 ou Sentinel-2 pour le Maroc")
    
    print("\n2. Données d'élévation:")
    print("   - NASA Earthdata: https://earthdata.nasa.gov/")
    print("   - SRTM 1 Arc-Second Global: https://lpdaac.usgs.gov/products/srtmgl1v003/")
    print("   - Tiles nécessaires: N20-40, W020-E010")
    
    print("\n3. Outils de traitement:")
    print("   - GDAL: https://gdal.org/ (conversion et découpage)")
    print("   - QGIS: https://qgis.org/ (visualisation et édition)")
    print("   - TileServer GL: https://github.com/maptiler/tileserver-gl")

def main():
    """Fonction principale"""
    print("🗺️ Téléchargement des données offline pour la carte du Maroc")
    print(f"📁 Dossier de destination: {BASE_DIR}")
    
    # Créer les dossiers
    IMAGERY_DIR.mkdir(parents=True, exist_ok=True)
    ELEVATION_DIR.mkdir(parents=True, exist_ok=True)
    
    # Créer des données d'exemple
    create_sample_tiles()
    create_sample_elevation_data()
    create_tile_structure_info()
    
    print("\n✅ Données d'exemple créées avec succès!")
    print("\n📋 Structure créée:")
    print(f"  {IMAGERY_DIR}/ - Tiles d'imagerie (PNG)")
    print(f"  {ELEVATION_DIR}/ - Données d'élévation")
    print(f"  {BASE_DIR}/config/ - Configuration")
    
    download_real_data()
    
    print(f"\n🚀 Vous pouvez maintenant tester la carte offline!")
    print("   Redémarrez le serveur de développement et accédez à /map-test")

if __name__ == "__main__":
    main()
