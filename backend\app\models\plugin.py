from sqlmodel import SQLModel, <PERSON>, Relationship
from typing import Optional, List, Dict, Any
from datetime import datetime
from enum import Enum
import uuid

class PluginStatus(str, Enum):
    INSTALLED = "installed"
    ACTIVE = "active"
    INACTIVE = "inactive"
    ERROR = "error"
    UPDATING = "updating"

class Plugin(SQLModel, table=True):
    """Modèle de plugin d'équipement"""
    __tablename__ = "plugins"
    
    id: Optional[int] = Field(default=None, primary_key=True)
    uuid: str = Field(default_factory=lambda: str(uuid.uuid4()), unique=True, index=True)
    
    # Informations de base
    name: str = Field(max_length=100, unique=True, index=True)
    display_name: str = Field(max_length=100)
    description: Optional[str] = Field(max_length=500)
    version: str = Field(max_length=20)
    author: Optional[str] = Field(max_length=100)
    
    # Statut
    status: PluginStatus = Field(default=PluginStatus.INSTALLED)
    is_enabled: bool = Field(default=True)
    
    # Chemins et fichiers
    plugin_path: str = Field(max_length=255)  # Chemin vers le dossier du plugin
    manifest_path: str = Field(max_length=255)  # Chemin vers manifest.json
    main_module: Optional[str] = Field(max_length=100)  # Module Python principal
    
    # Configuration
    configuration: Dict[str, Any] = Field(default_factory=dict, sa_column_kwargs={"type_": "JSON"})
    default_config: Dict[str, Any] = Field(default_factory=dict, sa_column_kwargs={"type_": "JSON"})
    
    # Métadonnées
    created_at: datetime = Field(default_factory=datetime.utcnow)
    updated_at: Optional[datetime] = Field(default=None)
    installed_at: datetime = Field(default_factory=datetime.utcnow)
    last_loaded: Optional[datetime] = Field(default=None)
    
    # Relations
    equipment: List["Equipment"] = Relationship(back_populates="plugin")

class PluginManifest(SQLModel, table=True):
    """Manifeste détaillé des plugins"""
    __tablename__ = "plugin_manifests"
    
    id: Optional[int] = Field(default=None, primary_key=True)
    plugin_id: int = Field(foreign_key="plugins.id", unique=True)
    
    # Informations du manifeste
    manifest_version: str = Field(max_length=10, default="1.0")
    api_version: str = Field(max_length=10, default="1.0")
    
    # Dépendances
    dependencies: List[str] = Field(default_factory=list, sa_column_kwargs={"type_": "JSON"})
    python_requirements: List[str] = Field(default_factory=list, sa_column_kwargs={"type_": "JSON"})
    
    # Capacités
    supported_equipment_types: List[str] = Field(default_factory=list, sa_column_kwargs={"type_": "JSON"})
    supported_protocols: List[str] = Field(default_factory=list, sa_column_kwargs={"type_": "JSON"})
    
    # Commandes disponibles
    commands: List[Dict[str, Any]] = Field(default_factory=list, sa_column_kwargs={"type_": "JSON"})
    
    # Interface utilisateur
    ui_components: Dict[str, Any] = Field(default_factory=dict, sa_column_kwargs={"type_": "JSON"})
    frontend_assets: List[str] = Field(default_factory=list, sa_column_kwargs={"type_": "JSON"})
    
    # Permissions requises
    required_permissions: List[str] = Field(default_factory=list, sa_column_kwargs={"type_": "JSON"})
    
    # Métadonnées
    license: Optional[str] = Field(max_length=50)
    homepage: Optional[str] = Field(max_length=255)
    repository: Optional[str] = Field(max_length=255)
    documentation: Optional[str] = Field(max_length=255)
    
    # Timestamps
    created_at: datetime = Field(default_factory=datetime.utcnow)
    updated_at: Optional[datetime] = Field(default=None)

class PluginLog(SQLModel, table=True):
    """Logs des plugins"""
    __tablename__ = "plugin_logs"
    
    id: Optional[int] = Field(default=None, primary_key=True)
    plugin_id: int = Field(foreign_key="plugins.id")
    
    # Informations du log
    level: str = Field(max_length=10, index=True)  # DEBUG, INFO, WARNING, ERROR, CRITICAL
    message: str = Field(max_length=1000)
    
    # Contexte
    function_name: Optional[str] = Field(max_length=100)
    line_number: Optional[int] = Field(default=None)
    
    # Données additionnelles
    extra_data: Optional[Dict[str, Any]] = Field(default_factory=dict, sa_column_kwargs={"type_": "JSON"})
    
    # Exception (si applicable)
    exception_type: Optional[str] = Field(max_length=100)
    exception_message: Optional[str] = Field(max_length=1000)
    traceback: Optional[str] = Field(max_length=5000)
    
    # Timestamp
    timestamp: datetime = Field(default_factory=datetime.utcnow, index=True)

class PluginMetrics(SQLModel, table=True):
    """Métriques de performance des plugins"""
    __tablename__ = "plugin_metrics"
    
    id: Optional[int] = Field(default=None, primary_key=True)
    plugin_id: int = Field(foreign_key="plugins.id")
    
    # Métriques de performance
    cpu_usage: Optional[float] = Field(default=None)  # Pourcentage
    memory_usage: Optional[float] = Field(default=None)  # MB
    execution_time: Optional[float] = Field(default=None)  # Secondes
    
    # Métriques d'utilisation
    commands_executed: int = Field(default=0)
    errors_count: int = Field(default=0)
    warnings_count: int = Field(default=0)
    
    # Métriques réseau (si applicable)
    bytes_sent: Optional[int] = Field(default=None)
    bytes_received: Optional[int] = Field(default=None)
    connections_active: Optional[int] = Field(default=None)
    
    # Métriques personnalisées
    custom_metrics: Dict[str, Any] = Field(default_factory=dict, sa_column_kwargs={"type_": "JSON"})
    
    # Timestamp
    timestamp: datetime = Field(default_factory=datetime.utcnow, index=True)
