import json
import asyncio
from typing import Dict, List, Set
from fastapi import WebSocket, WebSocketDisconnect
import structlog
from datetime import datetime

logger = structlog.get_logger()

class ConnectionManager:
    """Gestionnaire des connexions WebSocket"""
    
    def __init__(self):
        # Connexions actives par utilisateur
        self.active_connections: Dict[str, Set[WebSocket]] = {}
        # Connexions par rôle
        self.connections_by_role: Dict[str, Set[WebSocket]] = {}
        # Métadonnées des connexions
        self.connection_metadata: Dict[WebSocket, dict] = {}
        # Groupes de diffusion
        self.broadcast_groups: Dict[str, Set[WebSocket]] = {}
        
    async def connect(self, websocket: WebSocket, user_id: str, user_role: str, metadata: dict = None):
        """Accepter une nouvelle connexion WebSocket"""
        await websocket.accept()
        
        # Ajouter à la liste des connexions actives
        if user_id not in self.active_connections:
            self.active_connections[user_id] = set()
        self.active_connections[user_id].add(websocket)
        
        # Ajouter par rôle
        if user_role not in self.connections_by_role:
            self.connections_by_role[user_role] = set()
        self.connections_by_role[user_role].add(websocket)
        
        # Stocker les métadonnées
        self.connection_metadata[websocket] = {
            'user_id': user_id,
            'user_role': user_role,
            'connected_at': datetime.utcnow(),
            'last_ping': datetime.utcnow(),
            **(metadata or {})
        }
        
        logger.info(
            "Nouvelle connexion WebSocket",
            user_id=user_id,
            user_role=user_role,
            total_connections=self.get_connection_count()
        )
        
        # Envoyer un message de bienvenue
        await self.send_personal_message({
            'type': 'connection_established',
            'data': {
                'message': 'Connexion WebSocket établie',
                'server_time': datetime.utcnow().isoformat(),
                'user_id': user_id
            }
        }, websocket)

    def disconnect(self, websocket: WebSocket):
        """Supprimer une connexion WebSocket"""
        metadata = self.connection_metadata.get(websocket)
        if not metadata:
            return
            
        user_id = metadata['user_id']
        user_role = metadata['user_role']
        
        # Supprimer des connexions actives
        if user_id in self.active_connections:
            self.active_connections[user_id].discard(websocket)
            if not self.active_connections[user_id]:
                del self.active_connections[user_id]
        
        # Supprimer par rôle
        if user_role in self.connections_by_role:
            self.connections_by_role[user_role].discard(websocket)
            if not self.connections_by_role[user_role]:
                del self.connections_by_role[user_role]
        
        # Supprimer des groupes de diffusion
        for group_connections in self.broadcast_groups.values():
            group_connections.discard(websocket)
        
        # Supprimer les métadonnées
        del self.connection_metadata[websocket]
        
        logger.info(
            "Connexion WebSocket fermée",
            user_id=user_id,
            user_role=user_role,
            total_connections=self.get_connection_count()
        )

    async def send_personal_message(self, message: dict, websocket: WebSocket):
        """Envoyer un message à une connexion spécifique"""
        try:
            await websocket.send_text(json.dumps(message))
        except Exception as e:
            logger.error("Erreur lors de l'envoi du message personnel", error=str(e))
            self.disconnect(websocket)

    async def send_to_user(self, message: dict, user_id: str):
        """Envoyer un message à toutes les connexions d'un utilisateur"""
        if user_id in self.active_connections:
            disconnected = []
            for websocket in self.active_connections[user_id].copy():
                try:
                    await websocket.send_text(json.dumps(message))
                except Exception as e:
                    logger.error("Erreur lors de l'envoi à l'utilisateur", user_id=user_id, error=str(e))
                    disconnected.append(websocket)
            
            # Nettoyer les connexions fermées
            for websocket in disconnected:
                self.disconnect(websocket)

    async def send_to_role(self, message: dict, role: str):
        """Envoyer un message à tous les utilisateurs d'un rôle"""
        if role in self.connections_by_role:
            disconnected = []
            for websocket in self.connections_by_role[role].copy():
                try:
                    await websocket.send_text(json.dumps(message))
                except Exception as e:
                    logger.error("Erreur lors de l'envoi au rôle", role=role, error=str(e))
                    disconnected.append(websocket)
            
            # Nettoyer les connexions fermées
            for websocket in disconnected:
                self.disconnect(websocket)

    async def broadcast(self, message: dict, exclude_user: str = None):
        """Diffuser un message à toutes les connexions actives"""
        disconnected = []
        
        for user_id, connections in self.active_connections.items():
            if exclude_user and user_id == exclude_user:
                continue
                
            for websocket in connections.copy():
                try:
                    await websocket.send_text(json.dumps(message))
                except Exception as e:
                    logger.error("Erreur lors de la diffusion", user_id=user_id, error=str(e))
                    disconnected.append(websocket)
        
        # Nettoyer les connexions fermées
        for websocket in disconnected:
            self.disconnect(websocket)

    async def add_to_group(self, websocket: WebSocket, group_name: str):
        """Ajouter une connexion à un groupe de diffusion"""
        if group_name not in self.broadcast_groups:
            self.broadcast_groups[group_name] = set()
        self.broadcast_groups[group_name].add(websocket)

    async def remove_from_group(self, websocket: WebSocket, group_name: str):
        """Retirer une connexion d'un groupe de diffusion"""
        if group_name in self.broadcast_groups:
            self.broadcast_groups[group_name].discard(websocket)
            if not self.broadcast_groups[group_name]:
                del self.broadcast_groups[group_name]

    async def send_to_group(self, message: dict, group_name: str):
        """Envoyer un message à un groupe de diffusion"""
        if group_name in self.broadcast_groups:
            disconnected = []
            for websocket in self.broadcast_groups[group_name].copy():
                try:
                    await websocket.send_text(json.dumps(message))
                except Exception as e:
                    logger.error("Erreur lors de l'envoi au groupe", group=group_name, error=str(e))
                    disconnected.append(websocket)
            
            # Nettoyer les connexions fermées
            for websocket in disconnected:
                self.disconnect(websocket)

    def get_connection_count(self) -> int:
        """Obtenir le nombre total de connexions actives"""
        return sum(len(connections) for connections in self.active_connections.values())

    def get_user_connections(self, user_id: str) -> int:
        """Obtenir le nombre de connexions pour un utilisateur"""
        return len(self.active_connections.get(user_id, set()))

    def get_connections_by_role(self) -> Dict[str, int]:
        """Obtenir le nombre de connexions par rôle"""
        return {role: len(connections) for role, connections in self.connections_by_role.items()}

    def is_user_connected(self, user_id: str) -> bool:
        """Vérifier si un utilisateur est connecté"""
        return user_id in self.active_connections and len(self.active_connections[user_id]) > 0

    async def ping_all(self):
        """Envoyer un ping à toutes les connexions pour vérifier leur état"""
        ping_message = {
            'type': 'ping',
            'data': {
                'timestamp': datetime.utcnow().isoformat()
            }
        }
        await self.broadcast(ping_message)

    async def update_last_ping(self, websocket: WebSocket):
        """Mettre à jour le timestamp du dernier ping"""
        if websocket in self.connection_metadata:
            self.connection_metadata[websocket]['last_ping'] = datetime.utcnow()

# Instance globale du gestionnaire de connexions
manager = ConnectionManager()
