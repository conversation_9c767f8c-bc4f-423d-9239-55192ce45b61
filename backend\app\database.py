from sqlmodel import SQLModel, create_engine, Session
from sqlalchemy.ext.asyncio import AsyncSession, create_async_engine
from sqlalchemy.orm import sessionmaker
from geoalchemy2 import Geometry
import structlog

from app.config import settings

logger = structlog.get_logger()

# Moteur de base de données synchrone
engine = create_engine(
    settings.DATABASE_URL,
    echo=settings.DEBUG,
    pool_pre_ping=True,
    pool_recycle=300,
)

# Moteur de base de données asynchrone
async_engine = create_async_engine(
    settings.DATABASE_URL.replace("postgresql://", "postgresql+asyncpg://"),
    echo=settings.DEBUG,
    pool_pre_ping=True,
    pool_recycle=300,
)

# Session maker pour les sessions asynchrones
AsyncSessionLocal = sessionmaker(
    async_engine, class_=AsyncSession, expire_on_commit=False
)

def get_session():
    """Générateur de session de base de données synchrone"""
    with Session(engine) as session:
        try:
            yield session
        except Exception as e:
            logger.error("Erreur de session de base de données", error=str(e))
            session.rollback()
            raise
        finally:
            session.close()

async def get_async_session():
    """Générateur de session de base de données asynchrone"""
    async with AsyncSessionLocal() as session:
        try:
            yield session
        except Exception as e:
            logger.error("Erreur de session de base de données asynchrone", error=str(e))
            await session.rollback()
            raise
        finally:
            await session.close()

def create_db_and_tables():
    """Créer la base de données et les tables"""
    SQLModel.metadata.create_all(engine)
    logger.info("Tables de base de données créées")

def init_db():
    """Initialiser la base de données avec des données de base"""
    from app.models.user import User
    from app.core.security import get_password_hash
    
    with Session(engine) as session:
        # Créer un utilisateur admin par défaut
        admin_user = session.query(User).filter(User.email == "<EMAIL>").first()
        if not admin_user:
            admin_user = User(
                email="<EMAIL>",
                username="admin",
                full_name="Administrateur C2-EW",
                hashed_password=get_password_hash("admin123"),
                is_active=True,
                is_superuser=True,
                role="admin"
            )
            session.add(admin_user)
            session.commit()
            logger.info("Utilisateur admin créé", email=admin_user.email)
        
        logger.info("Base de données initialisée")
