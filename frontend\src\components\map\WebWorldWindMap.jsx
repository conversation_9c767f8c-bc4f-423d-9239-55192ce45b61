import React, { useEffect, useRef, useState } from 'react';

const WebWorldWindMap = ({ className, onMapClick, onMapReady }) => {
  const canvasRef = useRef(null);
  const mapInstanceRef = useRef(null);
  const isInitializedRef = useRef(false);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    // Vérifier si WebWorldWind est disponible
    if (typeof WorldWind === 'undefined') {
      setError('WebWorldWind library not loaded. Please check the script tag in index.html');
      setIsLoading(false);
      return;
    }

    if (isInitializedRef.current || !canvasRef.current) return;

    try {
      console.log('Initializing WebWorldWind...');
      
      // Créer le WorldWindow
      const wwd = new WorldWind.WorldWindow(canvasRef.current);
      
      // Configuration pour la région Maroc + voisins
      // Zone étendue : Maroc + Algérie + Mauritanie + Sud Espagne + Océan Atlantique
      const extendedSector = new WorldWind.Sector(
        15.0,  // Sud (Mauritanie)
        40.0,  // Nord (Sud de l'Espagne)
        -20.0, // Ouest (Océan Atlantique)
        10.0   // Est (Algérie)
      );

      // Position initiale centrée sur le Maroc
      wwd.navigator.lookAtLocation.latitude = 31.7917;  // Centre du Maroc
      wwd.navigator.lookAtLocation.longitude = -7.0926; // Centre du Maroc
      wwd.navigator.range = 2000000; // 2000km d'altitude pour voir toute la région

      // Configurer la projection 2D
      wwd.globe = new WorldWind.Globe2D();

      // Ajouter les couches de base
      addBaseLayers(wwd);

      // Configurer les limites de navigation
      wwd.navigator.lookAtLocation.latitude = Math.max(15.0, Math.min(40.0, wwd.navigator.lookAtLocation.latitude));
      wwd.navigator.lookAtLocation.longitude = Math.max(-20.0, Math.min(10.0, wwd.navigator.lookAtLocation.longitude));

      mapInstanceRef.current = wwd;
      isInitializedRef.current = true;

      // Gestionnaire de clic
      if (onMapClick) {
        const handleClick = (event) => {
          const pickList = wwd.pick(wwd.canvasCoordinates(event.clientX, event.clientY));
          if (pickList.objects.length > 0) {
            const position = pickList.objects[0].position;
            if (position) {
              onMapClick({
                lat: position.latitude,
                lng: position.longitude,
                originalEvent: event
              });
            }
          }
        };
        canvasRef.current.addEventListener('click', handleClick);
      }

      // Callback de prêt
      if (onMapReady) {
        onMapReady(wwd);
      }

      setIsLoading(false);
      console.log('WebWorldWind initialized successfully');

    } catch (err) {
      console.error('Error initializing WebWorldWind:', err);
      setError(`Failed to initialize WebWorldWind: ${err.message}`);
      setIsLoading(false);
    }
  }, [onMapClick, onMapReady]);

  // Fonction pour ajouter les couches de base
  const addBaseLayers = (wwd) => {
    try {
      // Couche d'imagerie de base (Blue Marble)
      const bmngLayer = new WorldWind.BMNGOneImageLayer();
      wwd.addLayer(bmngLayer);

      // Couche Landsat pour plus de détails
      const landsatLayer = new WorldWind.BMNGLandsatLayer();
      wwd.addLayer(landsatLayer);

      // Couches de contrôle
      const compassLayer = new WorldWind.CompassLayer();
      wwd.addLayer(compassLayer);

      const coordsLayer = new WorldWind.CoordinatesDisplayLayer(wwd);
      wwd.addLayer(coordsLayer);

      const controlsLayer = new WorldWind.ViewControlsLayer(wwd);
      wwd.addLayer(controlsLayer);

      console.log('Base layers added successfully');
    } catch (err) {
      console.error('Error adding base layers:', err);
      throw err;
    }
  };

  if (error) {
    return (
      <div className={`${className} flex items-center justify-center bg-gray-900 text-white`}>
        <div className="text-center p-8">
          <div className="text-red-400 text-xl mb-4">❌ Erreur WebWorldWind</div>
          <div className="text-sm text-gray-300">{error}</div>
          <div className="text-xs text-gray-500 mt-4">
            Vérifiez que le script WebWorldWind est chargé dans index.html
          </div>
        </div>
      </div>
    );
  }

  if (isLoading) {
    return (
      <div className={`${className} flex items-center justify-center bg-gray-900 text-white`}>
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-400 mx-auto mb-4"></div>
          <div className="text-lg">Chargement de la carte...</div>
          <div className="text-sm text-gray-400 mt-2">Initialisation WebWorldWind</div>
        </div>
      </div>
    );
  }

  return (
    <div className={`relative ${className}`}>
      <canvas
        ref={canvasRef}
        className="w-full h-full"
        style={{ display: 'block' }}
      />
      
      {/* Indicateur de région */}
      <div className="absolute top-4 left-4 bg-black bg-opacity-70 text-white px-3 py-2 rounded text-sm">
        🗺️ Maroc + Régions voisines (2D)
      </div>
    </div>
  );
};

export default WebWorldWindMap;
