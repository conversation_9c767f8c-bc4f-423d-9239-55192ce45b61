#!/usr/bin/env python3
"""
Point d'entrée principal de l'application C2-EW
Plateforme de Commandement et Contrôle pour la Guerre Électronique
"""

import uvicorn
import asyncio
from contextlib import asynccontextmanager
from fastapi import FastAPI, Request, Response, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from fastapi.middleware.trustedhost import TrustedHostMiddleware
from fastapi.staticfiles import StaticFiles
from fastapi.responses import FileResponse
import structlog
import os
from pathlib import Path

# Configuration du logging
structlog.configure(
    processors=[
        structlog.stdlib.filter_by_level,
        structlog.stdlib.add_logger_name,
        structlog.stdlib.add_log_level,
        structlog.stdlib.PositionalArgumentsFormatter(),
        structlog.processors.TimeStamper(fmt="iso"),
        structlog.processors.StackInfoRenderer(),
        structlog.processors.format_exc_info,
        structlog.processors.UnicodeDecoder(),
        structlog.processors.JSONRenderer()
    ],
    context_class=dict,
    logger_factory=structlog.stdlib.LoggerFactory(),
    wrapper_class=structlog.stdlib.BoundLogger,
    cache_logger_on_first_use=True,
)

logger = structlog.get_logger()

# Imports de l'application
from app.core.config import settings
from app.core.database import engine, create_tables
from app.core.security_manager import security_manager
from app.api.v1.api import api_router
from app.api.v1.websocket import router as websocket_router
from app.websocket.events import start_heartbeat

@asynccontextmanager
async def lifespan(app: FastAPI):
    """Gestionnaire du cycle de vie de l'application"""
    logger.info("Démarrage de l'application C2-EW")
    
    # Créer les tables de base de données
    try:
        await create_tables()
        logger.info("Tables de base de données créées/vérifiées")
    except Exception as e:
        logger.error("Erreur lors de la création des tables", error=str(e))
        raise
    
    # Démarrer les tâches en arrière-plan
    try:
        start_heartbeat()
        logger.info("Tâches WebSocket démarrées")
    except Exception as e:
        logger.error("Erreur lors du démarrage des tâches WebSocket", error=str(e))
    
    # Initialiser les plugins par défaut
    try:
        await initialize_default_plugins()
        logger.info("Plugins par défaut initialisés")
    except Exception as e:
        logger.error("Erreur lors de l'initialisation des plugins", error=str(e))
    
    logger.info("Application C2-EW démarrée avec succès")
    
    yield
    
    # Nettoyage lors de l'arrêt
    logger.info("Arrêt de l'application C2-EW")

# Créer l'application FastAPI
app = FastAPI(
    title="C2-EW Platform API",
    description="API pour la plateforme de Commandement et Contrôle - Guerre Électronique",
    version="1.0.0",
    docs_url="/api/docs" if settings.DEBUG else None,
    redoc_url="/api/redoc" if settings.DEBUG else None,
    openapi_url="/api/openapi.json" if settings.DEBUG else None,
    lifespan=lifespan
)

# Middleware de sécurité
@app.middleware("http")
async def security_middleware(request: Request, call_next):
    """Middleware de sécurité"""
    start_time = time.time()
    
    # Vérifier si l'IP est bloquée
    client_ip = request.client.host if request.client else "unknown"
    if security_manager.is_ip_blocked(client_ip):
        logger.warning("Tentative d'accès depuis une IP bloquée", ip=client_ip)
        return Response(status_code=403, content="IP bloquée")
    
    # Vérifier le rate limiting pour les endpoints sensibles
    if request.url.path.startswith("/api/v1/auth/"):
        if not security_manager.check_rate_limit(client_ip, max_attempts=10, window_minutes=15):
            logger.warning("Rate limit dépassé", ip=client_ip, path=request.url.path)
            return Response(status_code=429, content="Trop de tentatives")
    
    # Traiter la requête
    response = await call_next(request)
    
    # Ajouter les en-têtes de sécurité
    security_headers = security_manager.get_security_headers()
    for header, value in security_headers.items():
        response.headers[header] = value
    
    # Logger la requête
    process_time = time.time() - start_time
    logger.info("Requête HTTP",
               method=request.method,
               path=request.url.path,
               status_code=response.status_code,
               process_time=round(process_time, 4),
               client_ip=client_ip)
    
    return response

# Middleware CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=settings.ALLOWED_HOSTS,
    allow_credentials=True,
    allow_methods=["GET", "POST", "PUT", "DELETE", "PATCH", "OPTIONS"],
    allow_headers=["*"],
)

# Middleware pour les hôtes de confiance
if not settings.DEBUG:
    app.add_middleware(
        TrustedHostMiddleware,
        allowed_hosts=settings.ALLOWED_HOSTS
    )

# Inclure les routeurs API
app.include_router(api_router, prefix="/api/v1")
app.include_router(websocket_router, prefix="/api/v1")

# Configuration des tuiles vectorielles
TILES_DIR = Path("tiles")

@app.get("/tiles/{z}/{x}/{y}.pbf")
async def get_vector_tile(z: int, x: int, y: int):
    """
    Servir les tuiles vectorielles (.pbf) pour la carte offline du Maroc

    Args:
        z: Niveau de zoom (5-12)
        x: Coordonnée X de la tuile
        y: Coordonnée Y de la tuile

    Returns:
        FileResponse: Fichier .pbf de la tuile ou erreur 404
    """
    tile_path = TILES_DIR / str(z) / str(x) / f"{y}.pbf"

    if tile_path.exists():
        return FileResponse(
            tile_path,
            media_type="application/x-protobuf",
            headers={
                "Content-Encoding": "gzip",
                "Cache-Control": "public, max-age=86400",  # Cache 24h
                "Access-Control-Allow-Origin": "*"
            }
        )
    else:
        logger.warning("Tuile vectorielle non trouvée", z=z, x=x, y=y, path=str(tile_path))
        raise HTTPException(status_code=404, detail="Tuile non trouvée")

@app.get("/tiles/metadata")
async def get_tiles_metadata():
    """
    Métadonnées des tuiles vectorielles du Maroc

    Returns:
        dict: Informations sur les tuiles disponibles
    """
    return {
        "name": "Maroc - Tuiles Vectorielles",
        "description": "Tuiles vectorielles offline du Royaume du Maroc pour C2-EW",
        "version": "1.0.0",
        "attribution": "© Royaume du Maroc - C2-EW Platform",
        "bounds": [-17.1, 20.8, -1.0, 35.9],  # [ouest, sud, est, nord]
        "center": [-8.0, 29.0, 6],  # [lon, lat, zoom]
        "minzoom": 5,
        "maxzoom": 12,
        "format": "pbf",
        "type": "vector",
        "tiles": [f"{settings.API_V1_STR}/tiles/{{z}}/{{x}}/{{y}}.pbf"],
        "vector_layers": [
            {
                "id": "boundaries",
                "description": "Frontières du Royaume du Maroc",
                "fields": {
                    "name": "Nom en français",
                    "name_ar": "Nom en arabe",
                    "type": "Type de géométrie"
                }
            },
            {
                "id": "cities",
                "description": "Principales villes du Maroc",
                "fields": {
                    "name": "Nom de la ville",
                    "name_ar": "Nom en arabe",
                    "population": "Population",
                    "capital": "Capitale (oui/non)"
                }
            }
        ]
    }

# Servir les fichiers statiques du frontend
if settings.SERVE_STATIC:
    app.mount("/static", StaticFiles(directory="frontend/dist/assets"), name="static")
    
    @app.get("/")
    async def serve_frontend():
        """Servir l'application frontend"""
        return FileResponse("frontend/dist/index.html")
    
    @app.get("/{path:path}")
    async def serve_frontend_routes(path: str):
        """Servir les routes frontend (SPA)"""
        # Vérifier si c'est un fichier statique
        if "." in path and not path.startswith("api/"):
            try:
                return FileResponse(f"frontend/dist/{path}")
            except:
                pass
        
        # Sinon, servir l'index.html pour le routing côté client
        return FileResponse("frontend/dist/index.html")

# Gestionnaire d'erreurs global
@app.exception_handler(Exception)
async def global_exception_handler(request: Request, exc: Exception):
    """Gestionnaire d'erreurs global"""
    logger.error("Erreur non gérée",
                error=str(exc),
                path=request.url.path,
                method=request.method,
                client_ip=request.client.host if request.client else "unknown")
    
    if settings.DEBUG:
        import traceback
        return Response(
            status_code=500,
            content=f"Erreur interne: {str(exc)}\n\n{traceback.format_exc()}"
        )
    else:
        return Response(status_code=500, content="Erreur interne du serveur")

# Endpoint de santé
@app.get("/health")
async def health_check():
    """Vérification de l'état de santé de l'application"""
    return {
        "status": "healthy",
        "version": "1.0.0",
        "timestamp": datetime.utcnow().isoformat(),
        "database": "connected",  # Vous pourriez vérifier la DB ici
        "websocket_connections": len(security_manager.active_sessions)
    }

async def initialize_default_plugins():
    """Initialiser les plugins par défaut"""
    # Ici vous pourriez charger automatiquement certains plugins
    # ou vérifier leur état
    pass

if __name__ == "__main__":
    import time
    from datetime import datetime
    
    logger.info("Démarrage du serveur C2-EW",
               host=settings.HOST,
               port=settings.PORT,
               debug=settings.DEBUG)
    
    uvicorn.run(
        "main:app",
        host=settings.HOST,
        port=settings.PORT,
        reload=settings.DEBUG,
        log_level="info" if settings.DEBUG else "warning",
        access_log=settings.DEBUG,
        server_header=False,
        date_header=False
    )
