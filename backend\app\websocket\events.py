import asyncio
from typing import Dict, Any
from datetime import datetime
import structlog
from .manager import manager

logger = structlog.get_logger()

class EventBroadcaster:
    """Gestionnaire d'événements pour diffusion WebSocket"""
    
    @staticmethod
    async def equipment_status_changed(equipment_id: int, status: str, metadata: Dict[str, Any] = None):
        """Diffuser un changement de statut d'équipement"""
        message = {
            'type': 'equipment_status',
            'data': {
                'equipment_id': equipment_id,
                'status': status,
                'timestamp': datetime.utcnow().isoformat(),
                'metadata': metadata or {}
            }
        }
        await manager.broadcast(message)
        logger.info("Statut équipement diffusé", equipment_id=equipment_id, status=status)

    @staticmethod
    async def equipment_metrics_updated(equipment_id: int, metrics: Dict[str, Any]):
        """Diffuser les nouvelles métriques d'un équipement"""
        message = {
            'type': 'equipment_metrics',
            'data': {
                'equipment_id': equipment_id,
                'metrics': metrics,
                'timestamp': datetime.utcnow().isoformat()
            }
        }
        await manager.broadcast(message)

    @staticmethod
    async def new_alert(alert_data: Dict[str, Any]):
        """Diffuser une nouvelle alerte"""
        message = {
            'type': 'alert_new',
            'data': alert_data
        }
        
        # Diffuser selon le niveau d'alerte
        if alert_data.get('level') in ['critical', 'error']:
            # Alertes critiques à tous les utilisateurs
            await manager.broadcast(message)
        else:
            # Autres alertes seulement aux opérateurs et admins
            await manager.send_to_role(message, 'admin')
            await manager.send_to_role(message, 'operator')
        
        logger.info("Nouvelle alerte diffusée", level=alert_data.get('level'))

    @staticmethod
    async def alert_updated(alert_data: Dict[str, Any]):
        """Diffuser une mise à jour d'alerte"""
        message = {
            'type': 'alert_update',
            'data': alert_data
        }
        await manager.broadcast(message)

    @staticmethod
    async def command_status_updated(command_id: int, status: str, result: Dict[str, Any] = None):
        """Diffuser une mise à jour de statut de commande"""
        message = {
            'type': 'command_status',
            'data': {
                'command_id': command_id,
                'status': status,
                'result': result,
                'timestamp': datetime.utcnow().isoformat()
            }
        }
        await manager.broadcast(message)

    @staticmethod
    async def audio_stream_data(equipment_id: int, audio_data: bytes, format: str = 'pcm'):
        """Diffuser des données de streaming audio"""
        # Encoder les données audio en base64 pour transmission JSON
        import base64
        encoded_audio = base64.b64encode(audio_data).decode('utf-8')
        
        message = {
            'type': 'audio_stream',
            'data': {
                'equipment_id': equipment_id,
                'audio_data': encoded_audio,
                'format': format,
                'timestamp': datetime.utcnow().isoformat()
            }
        }
        
        # Diffuser seulement aux utilisateurs autorisés
        await manager.send_to_role(message, 'admin')
        await manager.send_to_role(message, 'operator')

    @staticmethod
    async def plugin_status_changed(plugin_id: int, status: str, message_text: str = None):
        """Diffuser un changement de statut de plugin"""
        message = {
            'type': 'plugin_status',
            'data': {
                'plugin_id': plugin_id,
                'status': status,
                'message': message_text,
                'timestamp': datetime.utcnow().isoformat()
            }
        }
        await manager.broadcast(message)

    @staticmethod
    async def system_notification(notification_type: str, title: str, message_text: str, level: str = 'info'):
        """Diffuser une notification système"""
        message = {
            'type': 'system_notification',
            'data': {
                'notification_type': notification_type,
                'title': title,
                'message': message_text,
                'level': level,
                'timestamp': datetime.utcnow().isoformat()
            }
        }
        await manager.broadcast(message)

    @staticmethod
    async def user_activity(user_id: str, activity: str, details: Dict[str, Any] = None):
        """Diffuser une activité utilisateur (pour les admins)"""
        message = {
            'type': 'user_activity',
            'data': {
                'user_id': user_id,
                'activity': activity,
                'details': details or {},
                'timestamp': datetime.utcnow().isoformat()
            }
        }
        # Diffuser seulement aux admins
        await manager.send_to_role(message, 'admin')

# Instance globale du broadcaster
broadcaster = EventBroadcaster()

# Tâche de heartbeat
async def heartbeat_task():
    """Tâche de heartbeat pour maintenir les connexions"""
    while True:
        try:
            await manager.ping_all()
            await asyncio.sleep(30)  # Ping toutes les 30 secondes
        except Exception as e:
            logger.error("Erreur dans la tâche de heartbeat", error=str(e))
            await asyncio.sleep(5)

# Démarrer la tâche de heartbeat
def start_heartbeat():
    """Démarrer la tâche de heartbeat en arrière-plan"""
    asyncio.create_task(heartbeat_task())
