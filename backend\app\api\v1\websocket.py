import json
from fastapi import <PERSON><PERSON>outer, WebSocket, WebSocketDisconnect, Depends, HTTPException, Query
from fastapi.security import HTT<PERSON><PERSON>earer
from sqlalchemy.orm import Session
import structlog

from app.core.database import get_db
from app.core.auth import get_current_user_from_token
from app.models.user import User
from app.websocket.manager import manager
from app.websocket.events import broadcaster, start_heartbeat

logger = structlog.get_logger()
router = APIRouter()
security = HTTPBearer()

@router.websocket("/ws")
async def websocket_endpoint(
    websocket: WebSocket,
    token: str = Query(...),
    db: Session = Depends(get_db)
):
    """Endpoint WebSocket principal"""
    try:
        # Authentifier l'utilisateur via le token
        user = await get_current_user_from_token(token, db)
        if not user:
            await websocket.close(code=4001, reason="Token invalide")
            return
        
        # Établir la connexion
        await manager.connect(
            websocket, 
            str(user.id), 
            user.role,
            {
                'username': user.username,
                'full_name': user.full_name,
                'ip_address': websocket.client.host if websocket.client else None
            }
        )
        
        # Démarrer le heartbeat si ce n'est pas déjà fait
        start_heartbeat()
        
        try:
            while True:
                # Recevoir les messages du client
                data = await websocket.receive_text()
                message = json.loads(data)
                
                # Traiter le message
                await handle_client_message(websocket, user, message)
                
        except WebSocketDisconnect:
            logger.info("Client WebSocket déconnecté", user_id=user.id)
        except Exception as e:
            logger.error("Erreur WebSocket", user_id=user.id, error=str(e))
        finally:
            manager.disconnect(websocket)
            
    except Exception as e:
        logger.error("Erreur lors de l'établissement de la connexion WebSocket", error=str(e))
        await websocket.close(code=4000, reason="Erreur de connexion")

async def handle_client_message(websocket: WebSocket, user: User, message: dict):
    """Traiter les messages reçus du client"""
    message_type = message.get('type')
    data = message.get('data', {})
    
    try:
        if message_type == 'ping':
            # Répondre au ping
            await manager.send_personal_message({
                'type': 'pong',
                'data': {
                    'timestamp': message.get('timestamp')
                }
            }, websocket)
            await manager.update_last_ping(websocket)
            
        elif message_type == 'pong':
            # Mettre à jour le timestamp du ping
            await manager.update_last_ping(websocket)
            
        elif message_type == 'subscribe_equipment':
            # S'abonner aux mises à jour d'un équipement spécifique
            equipment_id = data.get('equipment_id')
            if equipment_id:
                await manager.add_to_group(websocket, f"equipment_{equipment_id}")
                await manager.send_personal_message({
                    'type': 'subscription_confirmed',
                    'data': {
                        'subscription': 'equipment',
                        'equipment_id': equipment_id
                    }
                }, websocket)
                
        elif message_type == 'unsubscribe_equipment':
            # Se désabonner des mises à jour d'un équipement
            equipment_id = data.get('equipment_id')
            if equipment_id:
                await manager.remove_from_group(websocket, f"equipment_{equipment_id}")
                
        elif message_type == 'subscribe_alerts':
            # S'abonner aux alertes
            alert_level = data.get('level', 'all')
            group_name = f"alerts_{alert_level}" if alert_level != 'all' else "alerts"
            await manager.add_to_group(websocket, group_name)
            
        elif message_type == 'request_status':
            # Demander le statut actuel du système
            await send_system_status(websocket, user)
            
        elif message_type == 'audio_control':
            # Contrôler le streaming audio
            await handle_audio_control(websocket, user, data)
            
        else:
            logger.warning("Type de message WebSocket non reconnu", type=message_type, user_id=user.id)
            
    except Exception as e:
        logger.error("Erreur lors du traitement du message client", error=str(e), user_id=user.id)
        await manager.send_personal_message({
            'type': 'error',
            'data': {
                'message': 'Erreur lors du traitement du message',
                'original_type': message_type
            }
        }, websocket)

async def send_system_status(websocket: WebSocket, user: User):
    """Envoyer le statut actuel du système"""
    try:
        # Ici, vous collecteriez les données de statut réelles
        status_data = {
            'type': 'system_status',
            'data': {
                'server_time': datetime.utcnow().isoformat(),
                'connections': manager.get_connection_count(),
                'connections_by_role': manager.get_connections_by_role(),
                'user_role': user.role,
                'permissions': get_user_permissions(user)
            }
        }
        await manager.send_personal_message(status_data, websocket)
    except Exception as e:
        logger.error("Erreur lors de l'envoi du statut système", error=str(e))

async def handle_audio_control(websocket: WebSocket, user: User, data: dict):
    """Gérer les contrôles de streaming audio"""
    if not has_audio_permission(user):
        await manager.send_personal_message({
            'type': 'error',
            'data': {
                'message': 'Permissions insuffisantes pour le contrôle audio'
            }
        }, websocket)
        return
    
    action = data.get('action')
    equipment_id = data.get('equipment_id')
    
    if action == 'start_stream':
        # Démarrer le streaming audio pour un équipement
        await manager.add_to_group(websocket, f"audio_{equipment_id}")
        await manager.send_personal_message({
            'type': 'audio_stream_started',
            'data': {
                'equipment_id': equipment_id
            }
        }, websocket)
        
    elif action == 'stop_stream':
        # Arrêter le streaming audio
        await manager.remove_from_group(websocket, f"audio_{equipment_id}")
        await manager.send_personal_message({
            'type': 'audio_stream_stopped',
            'data': {
                'equipment_id': equipment_id
            }
        }, websocket)

def get_user_permissions(user: User) -> list:
    """Obtenir les permissions d'un utilisateur"""
    # Implémentation basée sur le rôle
    role_permissions = {
        'admin': ['*'],
        'operator': [
            'equipment:read', 'equipment:write', 'equipment:control',
            'plugin:read', 'plugin:configure',
            'user:read', 'system:logs', 'system:monitor',
            'geo:read', 'geo:write', 'audio:stream', 'audio:record'
        ],
        'technician': [
            'equipment:read', 'equipment:write',
            'plugin:read', 'plugin:configure',
            'user:read', 'system:logs', 'system:monitor',
            'geo:read', 'audio:stream'
        ],
        'viewer': [
            'equipment:read', 'plugin:read', 'user:read',
            'system:monitor', 'geo:read'
        ]
    }
    return role_permissions.get(user.role, [])

def has_audio_permission(user: User) -> bool:
    """Vérifier si l'utilisateur a les permissions audio"""
    permissions = get_user_permissions(user)
    return '*' in permissions or 'audio:stream' in permissions

# Endpoints REST pour les statistiques WebSocket
@router.get("/ws/stats")
async def get_websocket_stats(current_user: User = Depends(get_current_user_from_token)):
    """Obtenir les statistiques des connexions WebSocket"""
    if current_user.role != 'admin':
        raise HTTPException(status_code=403, detail="Accès refusé")
    
    return {
        'total_connections': manager.get_connection_count(),
        'connections_by_role': manager.get_connections_by_role(),
        'active_users': len(manager.active_connections),
        'broadcast_groups': len(manager.broadcast_groups)
    }

@router.post("/ws/broadcast")
async def broadcast_message(
    message: dict,
    current_user: User = Depends(get_current_user_from_token)
):
    """Diffuser un message à toutes les connexions (admin seulement)"""
    if current_user.role != 'admin':
        raise HTTPException(status_code=403, detail="Accès refusé")
    
    await manager.broadcast({
        'type': 'admin_broadcast',
        'data': {
            'message': message.get('message', ''),
            'from_user': current_user.username,
            'timestamp': datetime.utcnow().isoformat()
        }
    })
    
    return {'status': 'Message diffusé'}
