import React, { useEffect, useRef, useState } from 'react';

const SimpleWebWorldWindTest = () => {
  const canvasRef = useRef(null);
  const [status, setStatus] = useState('Initializing...');
  const [worldWindAvailable, setWorldWindAvailable] = useState(false);

  useEffect(() => {
    // Vérifier si WebWorldWind est disponible
    const checkWorldWind = () => {
      console.log('Checking WorldWind availability...');
      console.log('typeof WorldWind:', typeof WorldWind);
      console.log('window.WorldWind:', window.WorldWind);
      
      if (typeof WorldWind !== 'undefined') {
        setWorldWindAvailable(true);
        setStatus('WebWorldWind available - Initializing map...');
        initializeMap();
      } else {
        setStatus('WebWorldWind not available - Retrying...');
        setTimeout(checkWorldWind, 1000);
      }
    };

    const initializeMap = () => {
      try {
        if (!canvasRef.current) {
          setStatus('Canvas not ready');
          return;
        }

        console.log('Creating WorldWindow...');
        const wwd = new WorldWind.WorldWindow(canvasRef.current);
        
        console.log('WorldWindow created:', wwd);
        
        // Configuration simple
        wwd.navigator.lookAtLocation.latitude = 31.7917;
        wwd.navigator.lookAtLocation.longitude = -7.0926;
        wwd.navigator.range = 2000000;

        // Mode 2D
        wwd.globe = new WorldWind.Globe2D();

        // Ajouter une couche simple
        const bmngLayer = new WorldWind.BMNGOneImageLayer();
        wwd.addLayer(bmngLayer);

        setStatus('Map initialized successfully!');
        
      } catch (error) {
        console.error('Error initializing map:', error);
        setStatus(`Error: ${error.message}`);
      }
    };

    checkWorldWind();
  }, []);

  return (
    <div className="w-full h-full bg-gray-900 flex flex-col">
      {/* Status */}
      <div className="bg-gray-800 text-white p-4 text-sm">
        <div className="flex items-center space-x-4">
          <div className={`w-3 h-3 rounded-full ${
            worldWindAvailable ? 'bg-green-400' : 'bg-red-400'
          }`}></div>
          <span>Status: {status}</span>
        </div>
        
        <div className="mt-2 text-xs text-gray-400">
          <div>WebWorldWind Available: {worldWindAvailable ? 'Yes' : 'No'}</div>
          <div>Canvas Ready: {canvasRef.current ? 'Yes' : 'No'}</div>
        </div>
      </div>

      {/* Canvas */}
      <div className="flex-1 relative">
        <canvas
          ref={canvasRef}
          className="w-full h-full"
          width="1024"
          height="768"
          style={{ display: 'block' }}
        />
        
        {!worldWindAvailable && (
          <div className="absolute inset-0 flex items-center justify-center bg-gray-900 bg-opacity-75">
            <div className="text-center text-white">
              <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-400 mx-auto mb-4"></div>
              <div>Waiting for WebWorldWind...</div>
              <div className="text-sm text-gray-400 mt-2">{status}</div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default SimpleWebWorldWindTest;
