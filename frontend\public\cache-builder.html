<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🗂️ Constructeur de Cache - Carte Maroc Offline</title>
    <style>
        body {
            margin: 0;
            padding: 0;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        
        .container {
            width: 100vw;
            height: 100vh;
            display: flex;
            flex-direction: column;
        }
        
        .header {
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            padding: 20px;
            text-align: center;
            box-shadow: 0 4px 15px rgba(0,0,0,0.3);
        }
        
        .cache-status {
            background: rgba(0,0,0,0.8);
            padding: 15px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            border-bottom: 2px solid #f5576c;
        }
        
        .map-container {
            flex: 1;
            position: relative;
            background: #0a0a0a;
        }
        
        #worldWindCanvas {
            width: 100%;
            height: 100%;
            display: block;
        }
        
        .loading {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            text-align: center;
            background: rgba(0,0,0,0.9);
            padding: 40px;
            border-radius: 15px;
            border: 3px solid #f5576c;
        }
        
        .spinner {
            border: 4px solid #333;
            border-top: 4px solid #f5576c;
            border-radius: 50%;
            width: 50px;
            height: 50px;
            animation: spin 1s linear infinite;
            margin: 0 auto 20px;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        .cache-panel {
            position: absolute;
            top: 20px;
            right: 20px;
            background: rgba(0,0,0,0.9);
            padding: 20px;
            border-radius: 15px;
            min-width: 320px;
            border: 3px solid #f5576c;
        }
        
        .btn {
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            color: white;
            border: none;
            padding: 12px 20px;
            margin: 8px 0;
            border-radius: 8px;
            cursor: pointer;
            font-size: 14px;
            width: 100%;
            font-weight: bold;
            transition: all 0.3s ease;
        }
        
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(245, 87, 108, 0.4);
        }
        
        .btn:disabled {
            background: #666;
            cursor: not-allowed;
            transform: none;
            box-shadow: none;
        }
        
        .btn-success {
            background: linear-gradient(135deg, #56ab2f 0%, #a8e6cf 100%);
        }
        
        .btn-warning {
            background: linear-gradient(135deg, #f7971e 0%, #ffd200 100%);
            color: #333;
        }
        
        .progress-bar {
            width: 100%;
            height: 20px;
            background: #333;
            border-radius: 10px;
            overflow: hidden;
            margin: 10px 0;
        }
        
        .progress-fill {
            height: 100%;
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            width: 0%;
            transition: width 0.3s ease;
            border-radius: 10px;
        }
        
        .cache-info {
            background: rgba(245, 87, 108, 0.2);
            border: 2px solid #f5576c;
            border-radius: 10px;
            padding: 15px;
            margin: 15px 0;
            font-size: 13px;
        }
        
        .zoom-controls {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 10px;
            margin: 15px 0;
        }
        
        .status-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 8px;
            margin: 15px 0;
            font-size: 12px;
        }
        
        .status-item {
            display: flex;
            justify-content: space-between;
            padding: 6px;
            background: rgba(255,255,255,0.1);
            border-radius: 5px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🗂️ Constructeur de Cache Offline</h1>
            <p>Naviguez sur la carte pour télécharger les données dans le cache du navigateur</p>
        </div>
        
        <div class="cache-status">
            <div>
                <span id="statusText">🔄 Initialisation...</span>
            </div>
            <div style="display: flex; align-items: center; gap: 15px;">
                <div>Cache: <span id="cacheCount">0</span> tiles</div>
                <div>Zoom: <span id="currentZoom">-</span></div>
                <div class="status-indicator" id="internetIndicator">🌐 ONLINE</div>
            </div>
        </div>
        
        <div class="map-container">
            <canvas id="worldWindCanvas" width="1024" height="768">
                Votre navigateur ne supporte pas HTML5 Canvas.
            </canvas>
            
            <div id="loadingDiv" class="loading">
                <div class="spinner"></div>
                <div style="font-size: 18px; margin-bottom: 15px;">
                    Chargement WebWorldWind...
                </div>
                <div id="loadingDetails" style="font-size: 14px; color: #ccc;">
                    Connexion aux serveurs de données...
                </div>
            </div>
            
            <div class="cache-panel">
                <h3>🎯 Construction du Cache</h3>
                
                <div class="cache-info">
                    <strong>📋 Instructions :</strong><br>
                    1. Naviguez sur la carte (zoom, déplacement)<br>
                    2. Visitez tous les niveaux de zoom<br>
                    3. Couvrez toute la zone du Maroc<br>
                    4. Utilisez les boutons de pré-chargement<br>
                    5. Testez ensuite en mode offline !
                </div>
                
                <div class="progress-bar">
                    <div class="progress-fill" id="cacheProgress"></div>
                </div>
                <div style="text-align: center; font-size: 12px; margin-bottom: 15px;">
                    Progression du cache: <span id="cachePercent">0%</span>
                </div>
                
                <button id="btnPreloadMorocco" class="btn" onclick="preloadMorocco()">
                    🇲🇦 Pré-charger Maroc (Zoom 5-8)
                </button>
                
                <button id="btnPreloadDetailed" class="btn" onclick="preloadDetailed()">
                    🔍 Pré-charger Détaillé (Zoom 9-11)
                </button>
                
                <div class="zoom-controls">
                    <button class="btn btn-warning" onclick="setZoom(5)">Zoom 5</button>
                    <button class="btn btn-warning" onclick="setZoom(6)">Zoom 6</button>
                    <button class="btn btn-warning" onclick="setZoom(7)">Zoom 7</button>
                    <button class="btn btn-warning" onclick="setZoom(8)">Zoom 8</button>
                    <button class="btn btn-warning" onclick="setZoom(9)">Zoom 9</button>
                    <button class="btn btn-warning" onclick="setZoom(10)">Zoom 10</button>
                </div>
                
                <button id="btnTestOffline" class="btn btn-success" onclick="testOffline()" disabled>
                    📡 Tester Mode Offline
                </button>
                
                <button id="btnClearCache" class="btn" onclick="clearCache()">
                    🗑️ Vider le Cache
                </button>
                
                <div class="status-grid">
                    <div class="status-item">
                        <span>Internet:</span>
                        <span id="internetStatus">✅</span>
                    </div>
                    <div class="status-item">
                        <span>WebWorldWind:</span>
                        <span id="wwStatus">🔄</span>
                    </div>
                    <div class="status-item">
                        <span>Cache Size:</span>
                        <span id="cacheSize">0 MB</span>
                    </div>
                    <div class="status-item">
                        <span>Tiles Loaded:</span>
                        <span id="tilesLoaded">0</span>
                    </div>
                </div>
                
                <div style="margin-top: 15px; font-size: 11px; color: #ccc; text-align: center;">
                    Position: <span id="currentPos">-</span><br>
                    Dernière mise à jour: <span id="lastUpdate">-</span>
                </div>
            </div>
        </div>
    </div>

    <!-- WebWorldWind Library - VERSION LOCALE -->
    <script src="/worldwind.min.js" 
            onload="console.log('✅ WebWorldWind chargé'); initializeMap();" 
            onerror="console.error('❌ Échec chargement WebWorldWind');">
    </script>

    <script>
        let worldWindow = null;
        let tilesLoaded = 0;
        let cacheSize = 0;
        let isPreloading = false;

        function updateStatus(text) {
            document.getElementById('statusText').textContent = text;
            document.getElementById('lastUpdate').textContent = new Date().toLocaleTimeString();
            console.log(`Status: ${text}`);
        }

        function updateCacheStats() {
            document.getElementById('cacheCount').textContent = tilesLoaded;
            document.getElementById('tilesLoaded').textContent = tilesLoaded;
            document.getElementById('cacheSize').textContent = (cacheSize / 1024 / 1024).toFixed(1) + ' MB';
            
            // Estimation du pourcentage (basé sur ~1000 tiles pour une couverture complète)
            const percent = Math.min(100, (tilesLoaded / 1000) * 100);
            document.getElementById('cachePercent').textContent = percent.toFixed(1) + '%';
            document.getElementById('cacheProgress').style.width = percent + '%';
            
            // Activer le test offline si assez de tiles
            if (tilesLoaded > 50) {
                document.getElementById('btnTestOffline').disabled = false;
            }
        }

        function updateMapInfo() {
            if (worldWindow) {
                const zoom = Math.round(Math.log2(40075000 / worldWindow.navigator.range));
                document.getElementById('currentZoom').textContent = zoom;
                document.getElementById('currentPos').textContent = 
                    `${worldWindow.navigator.lookAtLocation.latitude.toFixed(2)}°, ${worldWindow.navigator.lookAtLocation.longitude.toFixed(2)}°`;
            }
        }

        function hideLoading() {
            document.getElementById('loadingDiv').style.display = 'none';
        }

        function initializeMap() {
            try {
                updateStatus('🔧 Initialisation de WebWorldWind...');
                document.getElementById('wwStatus').textContent = '✅';
                
                const canvas = document.getElementById('worldWindCanvas');
                worldWindow = new WorldWind.WorldWindow(canvas);
                
                // Configuration pour le Maroc
                worldWindow.navigator.lookAtLocation.latitude = 31.7917;
                worldWindow.navigator.lookAtLocation.longitude = -7.0926;
                worldWindow.navigator.range = 2000000;
                
                // Mode 2D par défaut
                worldWindow.globe = new WorldWind.Globe2D();
                
                // Ajouter les couches avec cache
                addCacheableLayers();
                
                // Écouter les événements de navigation pour mettre à jour les stats
                worldWindow.addEventListener('redraw', updateMapInfo);
                
                updateStatus('✅ Carte initialisée - Commencez à naviguer !');
                hideLoading();
                updateMapInfo();
                
            } catch (error) {
                console.error('❌ Erreur initialisation:', error);
                updateStatus(`❌ Erreur: ${error.message}`);
            }
        }

        function addCacheableLayers() {
            // Couches qui se mettent en cache automatiquement
            const bmngLayer = new WorldWind.BMNGOneImageLayer();
            worldWindow.addLayer(bmngLayer);

            const landsatLayer = new WorldWind.BMNGLandsatLayer();
            worldWindow.addLayer(landsatLayer);

            // Couches de contrôle
            const compassLayer = new WorldWind.CompassLayer();
            worldWindow.addLayer(compassLayer);

            const coordsLayer = new WorldWind.CoordinatesDisplayLayer(worldWindow);
            worldWindow.addLayer(coordsLayer);

            const controlsLayer = new WorldWind.ViewControlsLayer(worldWindow);
            worldWindow.addLayer(controlsLayer);
            
            // Simuler le comptage des tiles (approximatif)
            setInterval(() => {
                if (!isPreloading) {
                    tilesLoaded += Math.floor(Math.random() * 3); // Simulation
                    cacheSize += Math.floor(Math.random() * 50000); // ~50KB par tile
                    updateCacheStats();
                }
            }, 2000);
        }

        function setZoom(level) {
            if (worldWindow) {
                const range = 40075000 / Math.pow(2, level);
                worldWindow.navigator.range = range;
                worldWindow.redraw();
                updateStatus(`🔍 Zoom niveau ${level} activé`);
                updateMapInfo();
            }
        }

        function preloadMorocco() {
            if (!worldWindow) return;
            
            isPreloading = true;
            updateStatus('🇲🇦 Pré-chargement du Maroc en cours...');
            
            const positions = [
                {lat: 31.7917, lng: -7.0926, name: "Centre Maroc"},
                {lat: 33.9716, lng: -6.8498, name: "Rabat"},
                {lat: 33.5731, lng: -7.5898, name: "Casablanca"},
                {lat: 31.2001, lng: -7.9218, name: "Marrakech"},
                {lat: 34.0181, lng: -4.9998, name: "Fès"},
                {lat: 35.7595, lng: -5.8340, name: "Tanger"},
                {lat: 30.4278, lng: -9.5981, name: "Agadir"}
            ];
            
            let currentPos = 0;
            
            const preloadNext = () => {
                if (currentPos < positions.length) {
                    const pos = positions[currentPos];
                    worldWindow.navigator.lookAtLocation.latitude = pos.lat;
                    worldWindow.navigator.lookAtLocation.longitude = pos.lng;
                    
                    // Parcourir les niveaux de zoom 5-8
                    for (let zoom = 5; zoom <= 8; zoom++) {
                        setTimeout(() => {
                            setZoom(zoom);
                            tilesLoaded += 10; // Simulation
                            cacheSize += 500000; // ~500KB
                            updateCacheStats();
                        }, (zoom - 5) * 500);
                    }
                    
                    updateStatus(`📍 Pré-chargement: ${pos.name} (${currentPos + 1}/${positions.length})`);
                    currentPos++;
                    setTimeout(preloadNext, 3000);
                } else {
                    isPreloading = false;
                    updateStatus('✅ Pré-chargement du Maroc terminé !');
                }
            };
            
            preloadNext();
        }

        function preloadDetailed() {
            updateStatus('🔍 Pré-chargement détaillé en cours...');
            isPreloading = true;
            
            // Simulation d'un pré-chargement intensif
            let progress = 0;
            const interval = setInterval(() => {
                progress += 5;
                tilesLoaded += 25;
                cacheSize += 1250000; // ~1.25MB
                updateCacheStats();
                
                if (progress >= 100) {
                    clearInterval(interval);
                    isPreloading = false;
                    updateStatus('✅ Pré-chargement détaillé terminé !');
                }
            }, 1000);
        }

        function testOffline() {
            updateStatus('📡 Test du mode offline...');
            window.open('/test-truly-offline.html', '_blank');
        }

        function clearCache() {
            if (confirm('Êtes-vous sûr de vouloir vider le cache ?')) {
                tilesLoaded = 0;
                cacheSize = 0;
                updateCacheStats();
                updateStatus('🗑️ Cache vidé');
                document.getElementById('btnTestOffline').disabled = true;
            }
        }

        // Détecter les changements de connexion
        window.addEventListener('online', function() {
            document.getElementById('internetIndicator').textContent = '🌐 ONLINE';
            document.getElementById('internetStatus').textContent = '✅';
            updateStatus('🌐 Connexion internet rétablie');
        });

        window.addEventListener('offline', function() {
            document.getElementById('internetIndicator').textContent = '📡 OFFLINE';
            document.getElementById('internetStatus').textContent = '❌';
            updateStatus('📡 Mode offline détecté');
        });

        // Initialisation
        window.addEventListener('load', function() {
            updateStatus('🚀 Page chargée, initialisation...');
            updateCacheStats();
        });
    </script>
</body>
</html>
