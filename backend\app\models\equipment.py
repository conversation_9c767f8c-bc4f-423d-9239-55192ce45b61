from sqlmodel import SQLModel, Field, Relationship, Column
from geoalchemy2 import Geometry
from typing import Optional, List, Dict, Any
from datetime import datetime
from enum import Enum
import uuid

class EquipmentType(str, Enum):
    COMINT = "comint"
    ELINT = "elint"
    ANTI_DRONE = "anti_drone"
    JAMMER = "jammer"
    SENSOR = "sensor"
    RELAY = "relay"

class EquipmentStatus(str, Enum):
    ACTIVE = "active"
    STANDBY = "standby"
    OFFLINE = "offline"
    MAINTENANCE = "maintenance"
    ERROR = "error"

class Equipment(SQLModel, table=True):
    """Modèle d'équipement avec géolocalisation"""
    __tablename__ = "equipment"
    
    id: Optional[int] = Field(default=None, primary_key=True)
    uuid: str = Field(default_factory=lambda: str(uuid.uuid4()), unique=True, index=True)
    
    # Informations de base
    name: str = Field(max_length=100, index=True)
    description: Optional[str] = Field(max_length=500)
    equipment_type: EquipmentType
    model: Optional[str] = Field(max_length=100)
    serial_number: Optional[str] = Field(max_length=100, unique=True)
    
    # Statut
    status: EquipmentStatus = Field(default=EquipmentStatus.OFFLINE)
    is_active: bool = Field(default=True)
    
    # Géolocalisation (PostGIS)
    location: Optional[str] = Field(
        default=None,
        sa_column=Column(Geometry("POINT", srid=4326))
    )
    altitude: Optional[float] = Field(default=None)  # Altitude en mètres
    heading: Optional[float] = Field(default=None)   # Cap en degrés (0-360)
    
    # Configuration réseau
    ip_address: Optional[str] = Field(max_length=45)
    port: Optional[int] = Field(default=None)
    protocol: Optional[str] = Field(max_length=20, default="tcp")
    
    # Configuration série/USB
    serial_port: Optional[str] = Field(max_length=50)
    baud_rate: Optional[int] = Field(default=9600)
    usb_vendor_id: Optional[str] = Field(max_length=10)
    usb_product_id: Optional[str] = Field(max_length=10)
    
    # Plugin associé
    plugin_id: Optional[int] = Field(foreign_key="plugins.id")
    plugin: Optional["Plugin"] = Relationship(back_populates="equipment")
    
    # Configuration spécifique
    configuration: Dict[str, Any] = Field(default_factory=dict, sa_column_kwargs={"type_": "JSON"})
    capabilities: List[str] = Field(default_factory=list, sa_column_kwargs={"type_": "JSON"})
    
    # Métadonnées
    created_at: datetime = Field(default_factory=datetime.utcnow)
    updated_at: Optional[datetime] = Field(default=None)
    last_seen: Optional[datetime] = Field(default=None)
    
    # Relations
    command_history: List["CommandHistory"] = Relationship(back_populates="equipment")
    alerts: List["Alert"] = Relationship(back_populates="equipment")

class EquipmentMetrics(SQLModel, table=True):
    """Métriques de performance des équipements"""
    __tablename__ = "equipment_metrics"
    
    id: Optional[int] = Field(default=None, primary_key=True)
    equipment_id: int = Field(foreign_key="equipment.id")
    
    # Métriques système
    cpu_usage: Optional[float] = Field(default=None)  # Pourcentage
    memory_usage: Optional[float] = Field(default=None)  # Pourcentage
    disk_usage: Optional[float] = Field(default=None)  # Pourcentage
    temperature: Optional[float] = Field(default=None)  # Celsius
    
    # Métriques réseau
    network_rx_bytes: Optional[int] = Field(default=None)
    network_tx_bytes: Optional[int] = Field(default=None)
    network_latency: Optional[float] = Field(default=None)  # ms
    
    # Métriques spécifiques RF
    signal_strength: Optional[float] = Field(default=None)  # dBm
    noise_floor: Optional[float] = Field(default=None)  # dBm
    snr: Optional[float] = Field(default=None)  # dB
    frequency: Optional[float] = Field(default=None)  # Hz
    bandwidth: Optional[float] = Field(default=None)  # Hz
    
    # Métriques audio (pour récepteurs)
    audio_level: Optional[float] = Field(default=None)  # dB
    audio_quality: Optional[float] = Field(default=None)  # Score 0-100
    
    # Métadonnées personnalisées
    custom_metrics: Dict[str, Any] = Field(default_factory=dict, sa_column_kwargs={"type_": "JSON"})
    
    # Timestamp
    timestamp: datetime = Field(default_factory=datetime.utcnow, index=True)

class EquipmentGroup(SQLModel, table=True):
    """Groupes d'équipements pour organisation"""
    __tablename__ = "equipment_groups"
    
    id: Optional[int] = Field(default=None, primary_key=True)
    name: str = Field(max_length=100, unique=True)
    description: Optional[str] = Field(max_length=500)
    color: Optional[str] = Field(max_length=7, default="#2196F3")  # Couleur hex
    
    # Métadonnées
    created_at: datetime = Field(default_factory=datetime.utcnow)
    updated_at: Optional[datetime] = Field(default=None)

# Table de liaison many-to-many pour équipements et groupes
class EquipmentGroupMembership(SQLModel, table=True):
    """Association équipement-groupe"""
    __tablename__ = "equipment_group_memberships"
    
    equipment_id: int = Field(foreign_key="equipment.id", primary_key=True)
    group_id: int = Field(foreign_key="equipment_groups.id", primary_key=True)
    added_at: datetime = Field(default_factory=datetime.utcnow)
