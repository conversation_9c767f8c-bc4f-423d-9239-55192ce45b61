<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test WebWorldWind - Maroc 2D</title>
    <style>
        body {
            margin: 0;
            padding: 0;
            background-color: #1a1a1a;
            color: white;
            font-family: Arial, sans-serif;
        }
        
        .container {
            width: 100vw;
            height: 100vh;
            display: flex;
            flex-direction: column;
        }
        
        .header {
            background-color: #333;
            padding: 15px;
            border-bottom: 1px solid #555;
        }
        
        .status {
            background-color: #2a2a2a;
            padding: 10px 15px;
            border-bottom: 1px solid #555;
            font-size: 14px;
        }
        
        .map-container {
            flex: 1;
            position: relative;
            background-color: #0a0a0a;
        }
        
        #worldWindCanvas {
            width: 100%;
            height: 100%;
            display: block;
        }
        
        .loading {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            text-align: center;
        }
        
        .spinner {
            border: 3px solid #333;
            border-top: 3px solid #2196F3;
            border-radius: 50%;
            width: 40px;
            height: 40px;
            animation: spin 1s linear infinite;
            margin: 0 auto 20px;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        .controls {
            position: absolute;
            top: 20px;
            right: 20px;
            background-color: rgba(0,0,0,0.8);
            padding: 15px;
            border-radius: 8px;
            min-width: 200px;
        }
        
        .btn {
            background-color: #2196F3;
            color: white;
            border: none;
            padding: 8px 16px;
            margin: 5px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
        }
        
        .btn:hover {
            background-color: #1976D2;
        }
        
        .btn:disabled {
            background-color: #666;
            cursor: not-allowed;
        }
        
        .status-indicator {
            display: inline-block;
            width: 10px;
            height: 10px;
            border-radius: 50%;
            margin-right: 8px;
        }
        
        .status-ok { background-color: #4CAF50; }
        .status-error { background-color: #F44336; }
        .status-loading { background-color: #FF9800; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🗺️ Test WebWorldWind - Carte du Maroc 2D</h1>
            <p>Test direct de WebWorldWind sans framework React</p>
        </div>
        
        <div class="status">
            <span id="statusIndicator" class="status-indicator status-loading"></span>
            <span id="statusText">Initialisation...</span>
        </div>
        
        <div class="map-container">
            <canvas id="worldWindCanvas" width="1024" height="768">
                Votre navigateur ne supporte pas HTML5 Canvas.
            </canvas>
            
            <div id="loadingDiv" class="loading">
                <div class="spinner"></div>
                <div>Chargement de WebWorldWind...</div>
                <div id="loadingDetails" style="font-size: 12px; color: #ccc; margin-top: 10px;">
                    Vérification de la bibliothèque...
                </div>
            </div>
            
            <div class="controls">
                <h3>Contrôles</h3>
                <button id="btnMorocco" class="btn" onclick="zoomToMorocco()" disabled>
                    🇲🇦 Maroc
                </button>
                <button id="btnRegion" class="btn" onclick="zoomToRegion()" disabled>
                    🌍 Région
                </button>
                <button id="btnReload" class="btn" onclick="reloadMap()">
                    🔄 Recharger
                </button>
                
                <div style="margin-top: 15px; font-size: 12px;">
                    <div>WebWorldWind: <span id="wwStatus">❌</span></div>
                    <div>Canvas: <span id="canvasStatus">❌</span></div>
                    <div>Map: <span id="mapStatus">❌</span></div>
                </div>
            </div>
        </div>
    </div>

    <!-- WebWorldWind Library -->
    <script src="https://files.worldwind.arc.nasa.gov/artifactory/web/0.11.0/worldwind.min.js" 
            onload="console.log('✅ WebWorldWind loaded'); checkWorldWind();" 
            onerror="console.error('❌ Failed to load WebWorldWind'); showError('Impossible de charger WebWorldWind depuis le CDN');">
    </script>

    <script>
        let worldWindow = null;
        let attempts = 0;
        const maxAttempts = 20;

        function updateStatus(text, type = 'loading') {
            const indicator = document.getElementById('statusIndicator');
            const statusText = document.getElementById('statusText');
            
            indicator.className = `status-indicator status-${type}`;
            statusText.textContent = text;
            
            console.log(`Status: ${text}`);
        }

        function updateDiagnostics() {
            document.getElementById('wwStatus') = typeof WorldWind !== 'undefined' ? '✅' : '❌';
            document.getElementById('canvasStatus') = document.getElementById('worldWindCanvas') ? '✅' : '❌';
            document.getElementById('mapStatus') = worldWindow ? '✅' : '❌';
        }

        function showError(message) {
            updateStatus(`Erreur: ${message}`, 'error');
            document.getElementById('loadingDetails').textContent = message;
            updateDiagnostics();
        }

        function hideLoading() {
            document.getElementById('loadingDiv').style.display = 'none';
            document.getElementById('btnMorocco').disabled = false;
            document.getElementById('btnRegion').disabled = false;
        }

        function checkWorldWind() {
            attempts++;
            updateStatus(`Vérification WebWorldWind (${attempts}/${maxAttempts})...`);
            document.getElementById('loadingDetails').textContent = `Tentative ${attempts}/${maxAttempts}`;

            if (typeof WorldWind === 'undefined') {
                if (attempts >= maxAttempts) {
                    showError('WebWorldWind n\'a pas pu être chargé après 10 secondes');
                    return;
                }
                setTimeout(checkWorldWind, 500);
                return;
            }

            updateStatus('WebWorldWind détecté, initialisation de la carte...', 'loading');
            document.getElementById('wwStatus').textContent = '✅';
            initializeMap();
        }

        function initializeMap() {
            try {
                updateStatus('Création du WorldWindow...', 'loading');
                
                const canvas = document.getElementById('worldWindCanvas');
                if (!canvas) {
                    throw new Error('Canvas non trouvé');
                }
                
                document.getElementById('canvasStatus').textContent = '✅';
                
                // Créer le WorldWindow
                worldWindow = new WorldWind.WorldWindow(canvas);
                
                updateStatus('Configuration de la vue 2D...', 'loading');
                
                // Configuration pour le Maroc en mode 2D
                worldWindow.navigator.lookAtLocation.latitude = 31.7917;
                worldWindow.navigator.lookAtLocation.longitude = -7.0926;
                worldWindow.navigator.range = 2000000; // 2000km d'altitude
                
                // Mode 2D
                worldWindow.globe = new WorldWind.Globe2D();
                
                updateStatus('Ajout des couches cartographiques...', 'loading');
                
                // Ajouter les couches
                addLayers();
                
                updateStatus('Carte initialisée avec succès!', 'ok');
                document.getElementById('mapStatus').textContent = '✅';
                hideLoading();
                
                console.log('✅ Carte WebWorldWind initialisée avec succès');
                
            } catch (error) {
                console.error('❌ Erreur lors de l\'initialisation:', error);
                showError(`Erreur d'initialisation: ${error.message}`);
            }
        }

        function addLayers() {
            try {
                // Couche d'imagerie de base (Blue Marble)
                const bmngLayer = new WorldWind.BMNGOneImageLayer();
                worldWindow.addLayer(bmngLayer);

                // Couche Landsat pour plus de détails
                const landsatLayer = new WorldWind.BMNGLandsatLayer();
                worldWindow.addLayer(landsatLayer);

                // Couches de contrôle
                const compassLayer = new WorldWind.CompassLayer();
                worldWindow.addLayer(compassLayer);

                const coordsLayer = new WorldWind.CoordinatesDisplayLayer(worldWindow);
                worldWindow.addLayer(coordsLayer);

                const controlsLayer = new WorldWind.ViewControlsLayer(worldWindow);
                worldWindow.addLayer(controlsLayer);

                console.log('✅ Couches ajoutées avec succès');
            } catch (error) {
                console.error('❌ Erreur lors de l\'ajout des couches:', error);
                throw error;
            }
        }

        function zoomToMorocco() {
            if (worldWindow) {
                worldWindow.navigator.lookAtLocation.latitude = 31.7917;
                worldWindow.navigator.lookAtLocation.longitude = -7.0926;
                worldWindow.navigator.range = 1000000;
                worldWindow.redraw();
                updateStatus('Vue centrée sur le Maroc', 'ok');
            }
        }

        function zoomToRegion() {
            if (worldWindow) {
                worldWindow.navigator.lookAtLocation.latitude = 28.0;
                worldWindow.navigator.lookAtLocation.longitude = -5.0;
                worldWindow.navigator.range = 3000000;
                worldWindow.redraw();
                updateStatus('Vue élargie sur la région', 'ok');
            }
        }

        function reloadMap() {
            location.reload();
        }

        // Initialisation au chargement de la page
        window.addEventListener('load', function() {
            updateStatus('Page chargée, vérification de WebWorldWind...', 'loading');
            updateDiagnostics();
            
            // Si WebWorldWind n'est pas encore chargé, attendre
            if (typeof WorldWind === 'undefined') {
                setTimeout(checkWorldWind, 1000);
            } else {
                checkWorldWind();
            }
        });
    </script>
</body>
</html>
