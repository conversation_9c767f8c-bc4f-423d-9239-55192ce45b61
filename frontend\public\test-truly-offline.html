<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🔌 TEST VRAIMENT OFFLINE - Carte <PERSON>oc</title>
    <style>
        body {
            margin: 0;
            padding: 0;
            background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
            color: white;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        
        .container {
            width: 100vw;
            height: 100vh;
            display: flex;
            flex-direction: column;
        }
        
        .header {
            background: linear-gradient(135deg, #27ae60 0%, #2ecc71 100%);
            padding: 20px;
            text-align: center;
            box-shadow: 0 4px 15px rgba(0,0,0,0.3);
        }
        
        .offline-indicator {
            background-color: #e74c3c;
            color: white;
            padding: 10px;
            text-align: center;
            font-weight: bold;
            animation: pulse 2s infinite;
        }
        
        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.7; }
            100% { opacity: 1; }
        }
        
        .status-panel {
            background: rgba(0,0,0,0.8);
            padding: 15px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            border-bottom: 2px solid #27ae60;
        }
        
        .map-container {
            flex: 1;
            position: relative;
            background: #0a0a0a;
        }
        
        #worldWindCanvas {
            width: 100%;
            height: 100%;
            display: block;
        }
        
        .loading {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            text-align: center;
            background: rgba(0,0,0,0.9);
            padding: 40px;
            border-radius: 15px;
            border: 3px solid #27ae60;
        }
        
        .spinner {
            border: 4px solid #333;
            border-top: 4px solid #27ae60;
            border-radius: 50%;
            width: 50px;
            height: 50px;
            animation: spin 1s linear infinite;
            margin: 0 auto 20px;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        .controls {
            position: absolute;
            top: 20px;
            right: 20px;
            background: rgba(0,0,0,0.9);
            padding: 20px;
            border-radius: 15px;
            min-width: 280px;
            border: 3px solid #27ae60;
        }
        
        .btn {
            background: linear-gradient(135deg, #27ae60 0%, #2ecc71 100%);
            color: white;
            border: none;
            padding: 12px 20px;
            margin: 8px 0;
            border-radius: 8px;
            cursor: pointer;
            font-size: 14px;
            width: 100%;
            font-weight: bold;
            transition: all 0.3s ease;
        }
        
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(39, 174, 96, 0.4);
        }
        
        .btn:disabled {
            background: #666;
            cursor: not-allowed;
            transform: none;
            box-shadow: none;
        }
        
        .status-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 10px;
            margin: 15px 0;
            font-size: 12px;
        }
        
        .status-item {
            display: flex;
            justify-content: space-between;
            padding: 8px;
            background: rgba(255,255,255,0.1);
            border-radius: 5px;
        }
        
        .status-ok { color: #2ecc71; }
        .status-error { color: #e74c3c; }
        .status-loading { color: #f39c12; }
        
        .offline-badge {
            background: linear-gradient(135deg, #27ae60 0%, #2ecc71 100%);
            color: white;
            padding: 8px 15px;
            border-radius: 20px;
            font-size: 14px;
            font-weight: bold;
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .test-instructions {
            background: rgba(52, 152, 219, 0.2);
            border: 2px solid #3498db;
            border-radius: 10px;
            padding: 15px;
            margin: 15px 0;
            font-size: 13px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔌 TEST CARTE VRAIMENT OFFLINE</h1>
            <p>Vérification que la carte fonctionne sans connexion internet</p>
        </div>
        
        <div class="offline-indicator" id="offlineIndicator" style="display: none;">
            ⚠️ MODE OFFLINE DÉTECTÉ - Test en cours...
        </div>
        
        <div class="status-panel">
            <div>
                <span id="statusText">🔄 Initialisation du test offline...</span>
            </div>
            <div class="offline-badge">
                <span>📡</span>
                <span id="connectionStatus">Vérification...</span>
            </div>
        </div>
        
        <div class="map-container">
            <canvas id="worldWindCanvas" width="1024" height="768">
                Votre navigateur ne supporte pas HTML5 Canvas.
            </canvas>
            
            <div id="loadingDiv" class="loading">
                <div class="spinner"></div>
                <div style="font-size: 18px; margin-bottom: 15px;">
                    Chargement carte offline...
                </div>
                <div id="loadingDetails" style="font-size: 14px; color: #ccc;">
                    Test des composants offline...
                </div>
            </div>
            
            <div class="controls">
                <h3>🎛️ Contrôles Test Offline</h3>
                
                <div class="test-instructions">
                    <strong>📋 Instructions de test :</strong><br>
                    1. Déconnectez votre WiFi/Ethernet<br>
                    2. Ou activez le mode "Offline" dans F12 > Network<br>
                    3. Rafraîchissez cette page<br>
                    4. La carte doit toujours fonctionner !
                </div>
                
                <button id="btnTestConnection" class="btn" onclick="testConnection()">
                    🌐 Tester Connexion
                </button>
                
                <button id="btn2D" class="btn" onclick="switchTo2D()" disabled>
                    📐 Mode 2D
                </button>
                <button id="btn3D" class="btn" onclick="switchTo3D()" disabled>
                    🌍 Mode 3D
                </button>
                
                <button id="btnMorocco" class="btn" onclick="zoomToMorocco()" disabled>
                    🇲🇦 Centrer Maroc
                </button>
                <button id="btnRegion" class="btn" onclick="zoomToRegion()" disabled>
                    🌍 Vue Région
                </button>
                
                <div class="status-grid">
                    <div class="status-item">
                        <span>Internet:</span>
                        <span id="internetStatus" class="status-loading">🔄</span>
                    </div>
                    <div class="status-item">
                        <span>WebWorldWind:</span>
                        <span id="wwStatus" class="status-loading">🔄</span>
                    </div>
                    <div class="status-item">
                        <span>Config:</span>
                        <span id="configStatus" class="status-loading">🔄</span>
                    </div>
                    <div class="status-item">
                        <span>Tiles:</span>
                        <span id="tilesStatus" class="status-loading">🔄</span>
                    </div>
                    <div class="status-item">
                        <span>Canvas:</span>
                        <span id="canvasStatus" class="status-loading">🔄</span>
                    </div>
                    <div class="status-item">
                        <span>Carte:</span>
                        <span id="mapStatus" class="status-loading">🔄</span>
                    </div>
                </div>
                
                <div style="margin-top: 15px; font-size: 12px; color: #ccc; text-align: center;">
                    <div>Mode: <span id="currentMode">2D</span></div>
                    <div>Dernière mise à jour: <span id="lastUpdate">-</span></div>
                </div>
            </div>
        </div>
    </div>

    <!-- WebWorldWind Library - VERSION LOCALE OFFLINE -->
    <script src="/worldwind.min.js" 
            onload="console.log('✅ WebWorldWind chargé en LOCAL'); onWorldWindLoaded();" 
            onerror="console.error('❌ Échec chargement WebWorldWind LOCAL'); onWorldWindError();">
    </script>

    <script>
        let worldWindow = null;
        let currentMode = '2D';
        let isOnline = navigator.onLine;

        function updateStatus(text) {
            document.getElementById('statusText').textContent = text;
            document.getElementById('lastUpdate').textContent = new Date().toLocaleTimeString();
            console.log(`Status: ${text}`);
        }

        function updateComponentStatus(component, status) {
            const element = document.getElementById(component + 'Status');
            if (element) {
                if (status === true) {
                    element.textContent = '✅';
                    element.className = 'status-ok';
                } else if (status === false) {
                    element.textContent = '❌';
                    element.className = 'status-error';
                } else {
                    element.textContent = '🔄';
                    element.className = 'status-loading';
                }
            }
        }

        function hideLoading() {
            document.getElementById('loadingDiv').style.display = 'none';
            document.getElementById('btn2D').disabled = false;
            document.getElementById('btn3D').disabled = false;
            document.getElementById('btnMorocco').disabled = false;
            document.getElementById('btnRegion').disabled = false;
        }

        function showError(message) {
            updateStatus(`❌ Erreur: ${message}`);
            document.getElementById('loadingDetails').textContent = message;
        }

        async function testConnection() {
            updateStatus('🔍 Test de connexion internet...');
            
            try {
                const response = await fetch('https://www.google.com/favicon.ico', { 
                    mode: 'no-cors',
                    cache: 'no-cache'
                });
                updateComponentStatus('internet', true);
                document.getElementById('connectionStatus').textContent = 'ONLINE';
                updateStatus('🌐 Connexion internet détectée');
            } catch (error) {
                updateComponentStatus('internet', false);
                document.getElementById('connectionStatus').textContent = 'OFFLINE';
                document.getElementById('offlineIndicator').style.display = 'block';
                updateStatus('📡 Mode OFFLINE confirmé - Test réussi !');
            }
        }

        function onWorldWindLoaded() {
            updateComponentStatus('ww', true);
            updateStatus('✅ WebWorldWind chargé depuis le disque local');
            initializeOfflineMap();
        }

        function onWorldWindError() {
            updateComponentStatus('ww', false);
            showError('WebWorldWind n\'a pas pu être chargé depuis le disque local');
        }

        async function initializeOfflineMap() {
            try {
                updateStatus('🔧 Initialisation de la carte offline...');
                
                // Test de la configuration
                try {
                    const configResponse = await fetch('/worldwind-data/config/morocco-layers.json');
                    updateComponentStatus('config', configResponse.ok);
                } catch {
                    updateComponentStatus('config', false);
                }
                
                // Test des tiles
                try {
                    const tileResponse = await fetch('/worldwind-data/imagery/5/16/11.png');
                    updateComponentStatus('tiles', tileResponse.ok);
                } catch {
                    updateComponentStatus('tiles', false);
                }
                
                // Initialisation du canvas
                const canvas = document.getElementById('worldWindCanvas');
                updateComponentStatus('canvas', !!canvas);
                
                if (!canvas) {
                    throw new Error('Canvas non trouvé');
                }
                
                // Création du WorldWindow
                worldWindow = new WorldWind.WorldWindow(canvas);
                
                // Configuration pour le Maroc
                worldWindow.navigator.lookAtLocation.latitude = 31.7917;
                worldWindow.navigator.lookAtLocation.longitude = -7.0926;
                worldWindow.navigator.range = 2000000;
                
                // Mode 2D par défaut
                worldWindow.globe = new WorldWind.Globe2D();
                
                // Ajout des couches
                addLayers();
                
                updateComponentStatus('map', true);
                updateStatus('🎉 Carte offline initialisée avec succès !');
                hideLoading();
                
            } catch (error) {
                console.error('❌ Erreur initialisation:', error);
                updateComponentStatus('map', false);
                showError(`Erreur d'initialisation: ${error.message}`);
            }
        }

        function addLayers() {
            // Couches de base (fonctionnent même offline)
            const bmngLayer = new WorldWind.BMNGOneImageLayer();
            worldWindow.addLayer(bmngLayer);

            const compassLayer = new WorldWind.CompassLayer();
            worldWindow.addLayer(compassLayer);

            const coordsLayer = new WorldWind.CoordinatesDisplayLayer(worldWindow);
            worldWindow.addLayer(coordsLayer);

            const controlsLayer = new WorldWind.ViewControlsLayer(worldWindow);
            worldWindow.addLayer(controlsLayer);
        }

        function switchTo2D() {
            if (worldWindow) {
                worldWindow.globe = new WorldWind.Globe2D();
                currentMode = '2D';
                document.getElementById('currentMode').textContent = currentMode;
                updateStatus('📐 Mode 2D activé');
            }
        }

        function switchTo3D() {
            if (worldWindow) {
                worldWindow.globe = new WorldWind.Globe();
                currentMode = '3D';
                document.getElementById('currentMode').textContent = currentMode;
                updateStatus('🌍 Mode 3D activé');
            }
        }

        function zoomToMorocco() {
            if (worldWindow) {
                worldWindow.navigator.lookAtLocation.latitude = 31.7917;
                worldWindow.navigator.lookAtLocation.longitude = -7.0926;
                worldWindow.navigator.range = 1000000;
                worldWindow.redraw();
                updateStatus('🇲🇦 Vue centrée sur le Maroc');
            }
        }

        function zoomToRegion() {
            if (worldWindow) {
                worldWindow.navigator.lookAtLocation.latitude = 28.0;
                worldWindow.navigator.lookAtLocation.longitude = -5.0;
                worldWindow.navigator.range = 3000000;
                worldWindow.redraw();
                updateStatus('🌍 Vue élargie sur la région');
            }
        }

        // Initialisation
        window.addEventListener('load', function() {
            updateStatus('🚀 Page chargée, test de connexion...');
            testConnection();
            
            // Vérifier si WebWorldWind est déjà chargé
            if (typeof WorldWind !== 'undefined') {
                onWorldWindLoaded();
            }
        });

        // Détecter les changements de connexion
        window.addEventListener('online', function() {
            updateStatus('🌐 Connexion internet rétablie');
            testConnection();
        });

        window.addEventListener('offline', function() {
            updateStatus('📡 Connexion internet perdue - Mode offline');
            document.getElementById('offlineIndicator').style.display = 'block';
        });
    </script>
</body>
</html>
