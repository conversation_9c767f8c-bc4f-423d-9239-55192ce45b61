import React, { useEffect, useRef, useState } from 'react';

const OfflineWebWorldWind = ({ className, onMapClick, onMapReady, mode = '2D' }) => {
  const canvasRef = useRef(null);
  const mapInstanceRef = useRef(null);
  const isInitializedRef = useRef(false);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState(null);
  const [loadingStep, setLoadingStep] = useState('Initialisation...');

  useEffect(() => {
    let attempts = 0;
    const maxAttempts = 10; // Réduit car on est offline

    const checkWorldWind = () => {
      attempts++;
      setLoadingStep(`Vérification WebWorldWind (${attempts}/${maxAttempts})...`);
      
      if (typeof WorldWind === 'undefined') {
        if (attempts >= maxAttempts) {
          setError('WebWorldWind library not available. Please check the script tag.');
          setIsLoading(false);
          return;
        }
        setTimeout(checkWorldWind, 500);
        return;
      }

      if (isInitializedRef.current || !canvasRef.current) return;
      initializeOfflineMap();
    };

    const initializeOfflineMap = async () => {
      try {
        setLoadingStep('Chargement de la configuration...');
        
        // Charger la configuration offline
        const config = await loadOfflineConfig();
        
        setLoadingStep('Création du WorldWindow...');
        const wwd = new WorldWind.WorldWindow(canvasRef.current);
        
        setLoadingStep('Configuration de la vue...');
        // Configuration initiale
        wwd.navigator.lookAtLocation.latitude = config.region.center.latitude;
        wwd.navigator.lookAtLocation.longitude = config.region.center.longitude;
        wwd.navigator.range = 2000000;

        // Mode 2D ou 3D
        if (mode === '2D') {
          wwd.globe = new WorldWind.Globe2D();
        } else {
          wwd.globe = new WorldWind.Globe();
        }

        setLoadingStep('Ajout des couches offline...');
        await addOfflineLayers(wwd, config);

        mapInstanceRef.current = wwd;
        isInitializedRef.current = true;

        // Gestionnaire de clic
        if (onMapClick) {
          const handleClick = (event) => {
            const pickList = wwd.pick(wwd.canvasCoordinates(event.clientX, event.clientY));
            if (pickList.objects.length > 0) {
              const position = pickList.objects[0].position;
              if (position) {
                onMapClick({
                  lat: position.latitude,
                  lng: position.longitude,
                  originalEvent: event
                });
              }
            }
          };
          canvasRef.current.addEventListener('click', handleClick);
        }

        if (onMapReady) {
          onMapReady(wwd);
        }

        setIsLoading(false);
        setLoadingStep('Carte offline prête !');
        console.log('✅ Carte offline WebWorldWind initialisée');

      } catch (err) {
        console.error('❌ Erreur initialisation carte offline:', err);
        setError(`Erreur: ${err.message}`);
        setIsLoading(false);
      }
    };

    checkWorldWind();
  }, [onMapClick, onMapReady, mode]);

  const loadOfflineConfig = async () => {
    try {
      const response = await fetch('/worldwind-data/config/morocco-layers.json');
      if (!response.ok) {
        throw new Error('Configuration offline non trouvée');
      }
      return await response.json();
    } catch (error) {
      console.warn('Configuration offline non disponible, utilisation des valeurs par défaut');
      return getDefaultConfig();
    }
  };

  const getDefaultConfig = () => ({
    region: {
      center: { latitude: 31.7917, longitude: -7.0926 },
      bounds: { north: 40.0, south: 15.0, east: 10.0, west: -20.0 }
    },
    layers: {
      imagery: { enabled: true, offline: false },
      elevation: { enabled: true, offline: false }
    }
  });

  const addOfflineLayers = async (wwd, config) => {
    try {
      // Couche d'imagerie offline ou fallback
      if (config.layers.imagery.offline) {
        const offlineImagery = createOfflineImageryLayer(config.layers.imagery.config);
        wwd.addLayer(offlineImagery);
      } else {
        // Fallback vers les couches en ligne
        const bmngLayer = new WorldWind.BMNGOneImageLayer();
        wwd.addLayer(bmngLayer);
        
        const landsatLayer = new WorldWind.BMNGLandsatLayer();
        wwd.addLayer(landsatLayer);
      }

      // Données d'élévation offline ou fallback
      if (config.layers.elevation.offline) {
        const offlineElevation = createOfflineElevationModel(config.layers.elevation.config);
        wwd.globe.elevationModel = offlineElevation;
      }

      // Couches de contrôle
      if (config.layers.controls.compass.enabled) {
        const compassLayer = new WorldWind.CompassLayer();
        wwd.addLayer(compassLayer);
      }

      if (config.layers.controls.coordinates.enabled) {
        const coordsLayer = new WorldWind.CoordinatesDisplayLayer(wwd);
        wwd.addLayer(coordsLayer);
      }

      if (config.layers.controls.viewControls.enabled) {
        const controlsLayer = new WorldWind.ViewControlsLayer(wwd);
        wwd.addLayer(controlsLayer);
      }

    } catch (error) {
      console.warn('Erreur ajout couches offline, utilisation fallback:', error);
      addFallbackLayers(wwd);
    }
  };

  const createOfflineImageryLayer = (config) => {
    // Créer une couche d'imagerie offline personnalisée
    const sector = new WorldWind.Sector(
      config.sector.minLatitude,
      config.sector.maxLatitude,
      config.sector.minLongitude,
      config.sector.maxLongitude
    );

    const levelZeroDelta = new WorldWind.Location(
      config.levelZeroDelta.latitude,
      config.levelZeroDelta.longitude
    );

    return new WorldWind.TiledImageLayer(sector, levelZeroDelta, config.numLevels, config.format, config.service);
  };

  const createOfflineElevationModel = (config) => {
    const elevationModel = new WorldWind.ElevationModel();
    
    const sector = new WorldWind.Sector(
      config.sector.minLatitude,
      config.sector.maxLatitude,
      config.sector.minLongitude,
      config.sector.maxLongitude
    );

    const coverage = new WorldWind.TiledElevationCoverage(sector, config.resolution, config.service);
    elevationModel.addCoverage(coverage);
    
    return elevationModel;
  };

  const addFallbackLayers = (wwd) => {
    // Couches de base en cas d'échec des couches offline
    const bmngLayer = new WorldWind.BMNGOneImageLayer();
    wwd.addLayer(bmngLayer);

    const compassLayer = new WorldWind.CompassLayer();
    wwd.addLayer(compassLayer);

    const coordsLayer = new WorldWind.CoordinatesDisplayLayer(wwd);
    wwd.addLayer(coordsLayer);

    const controlsLayer = new WorldWind.ViewControlsLayer(wwd);
    wwd.addLayer(controlsLayer);
  };

  if (error) {
    return (
      <div className={`${className} flex items-center justify-center bg-gray-900 text-white`}>
        <div className="text-center p-8">
          <div className="text-red-400 text-xl mb-4">❌ Erreur Carte Offline</div>
          <div className="text-sm text-gray-300">{error}</div>
          <div className="text-xs text-gray-500 mt-4">
            Vérifiez que les données offline sont présentes dans /worldwind-data/
          </div>
        </div>
      </div>
    );
  }

  if (isLoading) {
    return (
      <div className={`${className} flex items-center justify-center bg-gray-900 text-white`}>
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-400 mx-auto mb-4"></div>
          <div className="text-lg">Chargement carte offline...</div>
          <div className="text-sm text-gray-400 mt-2">{loadingStep}</div>
        </div>
      </div>
    );
  }

  return (
    <div className={`relative ${className}`}>
      <canvas
        ref={canvasRef}
        className="w-full h-full"
        style={{ display: 'block' }}
      />
      
      {/* Indicateur offline */}
      <div className="absolute top-4 left-4 bg-green-600 bg-opacity-90 text-white px-3 py-2 rounded text-sm flex items-center">
        <div className="w-2 h-2 bg-green-300 rounded-full mr-2 animate-pulse"></div>
        🗺️ Carte Offline - Maroc {mode}
      </div>
    </div>
  );
};

export default OfflineWebWorldWind;
