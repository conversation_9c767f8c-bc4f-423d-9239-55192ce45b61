from sqlmodel import SQLModel, Field, Relationship
from typing import Optional, Dict, Any
from datetime import datetime
from enum import Enum
import uuid

class CommandStatus(str, Enum):
    PENDING = "pending"
    EXECUTING = "executing"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"
    TIMEOUT = "timeout"

class CommandPriority(str, Enum):
    LOW = "low"
    NORMAL = "normal"
    HIGH = "high"
    CRITICAL = "critical"
    EMERGENCY = "emergency"

class Command(SQLModel, table=True):
    """Modèle de commande d'équipement"""
    __tablename__ = "commands"
    
    id: Optional[int] = Field(default=None, primary_key=True)
    uuid: str = Field(default_factory=lambda: str(uuid.uuid4()), unique=True, index=True)
    
    # Informations de base
    name: str = Field(max_length=100, index=True)
    description: Optional[str] = Field(max_length=500)
    command_type: str = Field(max_length=50)  # control, query, config, etc.
    
    # Cible
    equipment_id: int = Field(foreign_key="equipment.id")
    equipment: "Equipment" = Relationship(back_populates="command_history")
    
    # Paramètres de la commande
    parameters: Dict[str, Any] = Field(default_factory=dict, sa_column_kwargs={"type_": "JSON"})
    
    # Statut et priorité
    status: CommandStatus = Field(default=CommandStatus.PENDING, index=True)
    priority: CommandPriority = Field(default=CommandPriority.NORMAL)
    
    # Résultat
    result: Optional[Dict[str, Any]] = Field(default=None, sa_column_kwargs={"type_": "JSON"})
    error_message: Optional[str] = Field(max_length=1000)
    
    # Timing
    created_at: datetime = Field(default_factory=datetime.utcnow, index=True)
    scheduled_at: Optional[datetime] = Field(default=None)
    started_at: Optional[datetime] = Field(default=None)
    completed_at: Optional[datetime] = Field(default=None)
    timeout_seconds: Optional[int] = Field(default=30)
    
    # Utilisateur
    created_by_id: int = Field(foreign_key="users.id")
    created_by: "User" = Relationship(back_populates="command_history")
    
    # Métadonnées
    metadata: Dict[str, Any] = Field(default_factory=dict, sa_column_kwargs={"type_": "JSON"})

class CommandHistory(SQLModel, table=True):
    """Historique détaillé des commandes"""
    __tablename__ = "command_history"
    
    id: Optional[int] = Field(default=None, primary_key=True)
    command_id: int = Field(foreign_key="commands.id")
    
    # Informations de l'événement
    event_type: str = Field(max_length=50)  # created, started, progress, completed, failed
    message: Optional[str] = Field(max_length=1000)
    
    # Données de l'événement
    event_data: Optional[Dict[str, Any]] = Field(default=None, sa_column_kwargs={"type_": "JSON"})
    
    # Timestamp
    timestamp: datetime = Field(default_factory=datetime.utcnow, index=True)

class CommandTemplate(SQLModel, table=True):
    """Templates de commandes prédéfinies"""
    __tablename__ = "command_templates"
    
    id: Optional[int] = Field(default=None, primary_key=True)
    uuid: str = Field(default_factory=lambda: str(uuid.uuid4()), unique=True, index=True)
    
    # Informations de base
    name: str = Field(max_length=100, unique=True, index=True)
    description: Optional[str] = Field(max_length=500)
    category: str = Field(max_length=50)  # frequency, audio, system, etc.
    
    # Type d'équipement compatible
    equipment_types: list[str] = Field(default_factory=list, sa_column_kwargs={"type_": "JSON"})
    plugin_id: Optional[int] = Field(foreign_key="plugins.id")
    
    # Template de commande
    command_template: Dict[str, Any] = Field(sa_column_kwargs={"type_": "JSON"})
    parameter_schema: Dict[str, Any] = Field(sa_column_kwargs={"type_": "JSON"})  # JSON Schema
    
    # Configuration
    default_priority: CommandPriority = Field(default=CommandPriority.NORMAL)
    default_timeout: int = Field(default=30)
    
    # Métadonnées
    is_active: bool = Field(default=True)
    created_at: datetime = Field(default_factory=datetime.utcnow)
    updated_at: Optional[datetime] = Field(default=None)
    created_by_id: int = Field(foreign_key="users.id")

class CommandQueue(SQLModel, table=True):
    """File d'attente des commandes"""
    __tablename__ = "command_queue"
    
    id: Optional[int] = Field(default=None, primary_key=True)
    command_id: int = Field(foreign_key="commands.id", unique=True)
    
    # Position dans la queue
    queue_position: int = Field(index=True)
    
    # Statut de la queue
    is_processing: bool = Field(default=False)
    worker_id: Optional[str] = Field(max_length=100)  # ID du worker qui traite
    
    # Timing
    queued_at: datetime = Field(default_factory=datetime.utcnow)
    processing_started_at: Optional[datetime] = Field(default=None)
    
    # Retry logic
    retry_count: int = Field(default=0)
    max_retries: int = Field(default=3)
    next_retry_at: Optional[datetime] = Field(default=None)
